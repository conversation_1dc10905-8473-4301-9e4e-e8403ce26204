package top.cywin.onetv.movie.player;

import top.cywin.onetv.movie.Constants;
import top.cywin.onetv.movie.bean.Channel;
import top.cywin.onetv.movie.bean.Episode;
import top.cywin.onetv.movie.bean.Flag;
import top.cywin.onetv.movie.bean.Result;
import top.cywin.onetv.movie.player.extractor.Force;
import top.cywin.onetv.movie.player.extractor.JianPian;
import top.cywin.onetv.movie.player.extractor.Push;
import top.cywin.onetv.movie.player.extractor.TVBus;
import top.cywin.onetv.movie.player.extractor.Thunder;
import top.cywin.onetv.movie.player.extractor.Video;
import top.cywin.onetv.movie.player.extractor.Youtube;
import top.cywin.onetv.movie.utils.UrlUtil;
import top.cywin.onetv.movie.utils.VodFlowTracker;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public class Source {

    private final List<Extractor> extractors;

    private static class Loader {
        static volatile Source INSTANCE = new Source();
    }

    public static Source get() {
        return Loader.INSTANCE;
    }

    public Source() {
        extractors = new ArrayList<>();
        extractors.add(new Force());
        extractors.add(new JianPian());
        extractors.add(new Push());
        extractors.add(new Thunder());
        extractors.add(new TVBus());
        extractors.add(new Video());
        extractors.add(new Youtube());
    }

    private Extractor getExtractor(String url) {
        // 🔥 关键修复：获取当前FlowID用于提取器选择日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        String host = UrlUtil.host(url);
        String scheme = UrlUtil.scheme(url);

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[EXTRACTOR_SELECT] 选择提取器: scheme=" + scheme + ", host=" + host);

        for (Extractor extractor : extractors) {
            if (extractor.match(scheme, host)) {
                android.util.Log.d("VOD_FLOW", flowIdPrefix + "[EXTRACTOR_FOUND] 找到匹配的提取器: " + extractor.getClass().getSimpleName());
                return extractor;
            }
        }

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[EXTRACTOR_NOT_FOUND] 未找到匹配的提取器，使用默认处理");
        return null;
    }

    private void addCallable(Iterator<Episode> iterator, List<Callable<List<Episode>>> items) {
        // 🔥 关键修复：获取当前FlowID用于解析器添加日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        String url = iterator.next().getUrl();

        if (Thunder.Parser.match(url)) {
            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[PARSER_ADD] 添加Thunder解析器: " + url);
            items.add(Thunder.Parser.get(url));
            iterator.remove();
        } else if (Youtube.Parser.match(url)) {
            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[PARSER_ADD] 添加Youtube解析器: " + url);
            items.add(Youtube.Parser.get(url));
            iterator.remove();
        }
    }

    public void parse(List<Flag> flags) throws Exception {
        // 🔥 关键修复：获取当前FlowID用于解析流程日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_PARSE_START] 开始解析播放源，线路数量: " + flags.size());

        for (Flag flag : flags) {
            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_PARSE_FLAG] 解析线路: " + flag.getFlag() + ", 剧集数量: " + flag.getEpisodes().size());

            ExecutorService executor = Executors.newFixedThreadPool(Constants.THREAD_POOL);
            List<Callable<List<Episode>>> items = new ArrayList<>();
            Iterator<Episode> iterator = flag.getEpisodes().iterator();

            while (iterator.hasNext()) addCallable(iterator, items);

            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_PARSE_TASKS] 创建解析任务数量: " + items.size());

            for (Future<List<Episode>> future : executor.invokeAll(items, 30, TimeUnit.SECONDS)) {
                List<Episode> episodes = future.get();
                flag.getEpisodes().addAll(episodes);
                android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_PARSE_RESULT] 解析完成，获得剧集数量: " + episodes.size());
            }

            executor.shutdownNow();
        }

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_PARSE_COMPLETE] 播放源解析完成");
    }

    public String fetch(Result result) throws Exception {
        // 🔥 关键修复：获取当前FlowID用于播放源获取日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        String url = result.getUrl().v();
        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_START] 开始获取播放源: " + url);

        Extractor extractor = getExtractor(url);
        if (extractor != null) {
            result.setParse(0);
            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_EXTRACTOR] 使用提取器: " + extractor.getClass().getSimpleName());
        }
        if (extractor instanceof Video) {
            result.setParse(1);
            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_VIDEO] 检测到Video提取器，设置解析模式");
        }

        String finalUrl = extractor == null ? url : extractor.fetch(url);

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_RESULT] 获取到播放URL: " + finalUrl);

        // 🔧 修复：处理URL编码的中文文件名 - 根据JianPian提取器的编码方式
        if (finalUrl != null && finalUrl.contains("127.0.0.1") && finalUrl.contains("%")) {
            try {
                // 🔥 关键修复：直接对URL中的编码部分进行GBK解码
                // 不能使用URI.getPath()，因为它会自动用UTF-8解码，导致GBK编码的中文乱码

                // 找到路径部分（从第三个/开始）
                int pathStart = finalUrl.indexOf("/", finalUrl.indexOf("://") + 3);
                if (pathStart != -1) {
                    String baseUrl = finalUrl.substring(0, pathStart);
                    String encodedPath = finalUrl.substring(pathStart);

                    android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_DECODE] 原始编码路径: " + encodedPath);

                    String decodedPath = null;
                    try {
                        // 🔥 直接对编码的路径进行GBK解码
                        decodedPath = java.net.URLDecoder.decode(encodedPath, "GBK");
                        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_DECODE] GBK解码成功: " + encodedPath + " -> " + decodedPath);
                    } catch (Exception e1) {
                        try {
                            // GBK失败，尝试UTF-8解码
                            decodedPath = java.net.URLDecoder.decode(encodedPath, "UTF-8");
                            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_DECODE] UTF-8解码成功: " + encodedPath + " -> " + decodedPath);
                        } catch (Exception e2) {
                            // 都失败，使用原始路径
                            decodedPath = encodedPath;
                            android.util.Log.w("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_DECODE] 所有解码方式失败，使用原始路径: " + encodedPath);
                        }
                    }

                    finalUrl = baseUrl + decodedPath;
                    android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_DECODE] URL解码完成: " + url + " -> " + finalUrl);
                } else {
                    android.util.Log.w("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_DECODE] 无法找到路径部分，跳过解码");
                }
            } catch (Exception e) {
                android.util.Log.w("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_DECODE_ERROR] URL解码失败，使用原始URL: " + finalUrl, e);
                // 解码失败时使用原始URL
            }
        }

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_FETCH_COMPLETE] 播放源获取完成: " + finalUrl);
        return finalUrl;
    }

    public String fetch(Channel channel) throws Exception {
        // 🔥 关键修复：获取当前FlowID用于频道播放源获取日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        String url = channel.getCurrent();
        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[CHANNEL_FETCH_START] 开始获取频道播放源: " + url);

        Extractor extractor = getExtractor(url);
        if (extractor != null) {
            channel.setParse(0);
            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[CHANNEL_FETCH_EXTRACTOR] 使用提取器: " + extractor.getClass().getSimpleName());
        }
        if (extractor instanceof Video) {
            channel.setParse(1);
            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[CHANNEL_FETCH_VIDEO] 检测到Video提取器");
        }

        String finalUrl = extractor == null ? url : extractor.fetch(url);
        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[CHANNEL_FETCH_COMPLETE] 频道播放源获取完成: " + finalUrl);

        return finalUrl;
    }

    public void stop() {
        // 🔥 关键修复：获取当前FlowID用于停止日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_STOP] 停止所有提取器");

        if (extractors == null) return;
        for (Extractor extractor : extractors) {
            try {
                extractor.stop();
                android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_STOP_EXTRACTOR] 停止提取器: " + extractor.getClass().getSimpleName());
            } catch (Exception e) {
                android.util.Log.w("VOD_FLOW", flowIdPrefix + "[SOURCE_STOP_ERROR] 停止提取器失败: " + extractor.getClass().getSimpleName(), e);
            }
        }
    }

    public void exit() {
        // 🔥 关键修复：获取当前FlowID用于退出日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_EXIT] 退出所有提取器");

        if (extractors == null) return;
        for (Extractor extractor : extractors) {
            try {
                extractor.exit();
                android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SOURCE_EXIT_EXTRACTOR] 退出提取器: " + extractor.getClass().getSimpleName());
            } catch (Exception e) {
                android.util.Log.w("VOD_FLOW", flowIdPrefix + "[SOURCE_EXIT_ERROR] 退出提取器失败: " + extractor.getClass().getSimpleName(), e);
            }
        }
    }

    public interface Extractor {

        boolean match(String scheme, String host);

        String fetch(String url) throws Exception;

        void stop();

        void exit();
    }
}
