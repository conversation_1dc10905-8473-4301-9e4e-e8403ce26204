package top.cywin.onetv.movie.server;

import top.cywin.onetv.movie.player.Players;
import top.cywin.onetv.movie.utils.VodFlowTracker;
import com.github.catvod.Proxy;
import com.github.catvod.utils.Util;

public class Server {

    private Players player;
    private Nano nano;

    private static class Loader {
        static volatile Server INSTANCE = new Server();
    }

    public static Server get() {
        return Loader.INSTANCE;
    }

    public Players getPlayer() {
        return player;
    }

    public void setPlayer(Players player) {
        this.player = player;
    }

    public String getAddress() {
        return getAddress(false);
    }

    public String getAddress(int tab) {
        return getAddress(false) + "?tab=" + tab;
    }

    public String getAddress(String path) {
        return getAddress(true) + path;
    }

    public String getAddress(boolean local) {
        return "http://" + (local ? "127.0.0.1" : Util.getIp()) + ":" + Proxy.getPort();
    }

    public void start() {
        // 🔥 关键修复：获取当前FlowID用于服务器启动日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        if (nano != null) {
            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SERVER_STATUS] 服务器已启动，端口: " + Proxy.getPort());
            return;
        }

        android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SERVER_START] 开始启动本地服务器，尝试端口范围: 9978-9999");

        for (int i = 9978; i < 9999; i++) {
            try {
                android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SERVER_TRY_PORT] 尝试启动端口: " + i);
                nano = new Nano(i);
                nano.start(500);
                Proxy.set(i);
                android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SERVER_SUCCESS] 本地服务器启动成功，端口: " + i);
                android.util.Log.d("VOD_FLOW", flowIdPrefix + "[SERVER_ADDRESS] 服务器地址: " + getAddress(true));
                break;
            } catch (Throwable e) {
                android.util.Log.w("VOD_FLOW", flowIdPrefix + "[SERVER_PORT_FAILED] 端口 " + i + " 启动失败: " + e.getMessage());
                nano = null;
            }
        }

        if (nano == null) {
            android.util.Log.e("VOD_FLOW", flowIdPrefix + "[SERVER_FAILED] 所有端口都启动失败，服务器启动失败");
        }
    }

    public void stop() {
        if (nano != null) nano.stop();
        nano = null;
    }
}
