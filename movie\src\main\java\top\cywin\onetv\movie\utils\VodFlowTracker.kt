package top.cywin.onetv.movie.utils

import android.util.Log
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * VOD播放流程跟踪器 - 参考FongMi_TV原版日志系统
 * 用于跟踪从点击电影到播放完成的全流程
 */
object VodFlowTracker {
    
    private const val TAG_MAIN = "VOD_FLOW"
    private const val TAG_SUB = "-VOD_FLOW"
    
    private val timeFormat = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
    private val flowStartTimes = ConcurrentHashMap<String, Long>()

    // 🔥 FlowID上下文管理 - 用于跨组件传递FlowID
    private val flowIdContext = ThreadLocal<String?>()

    // 🔥 全局FlowID管理 - 用于跨页面传递FlowID
    @Volatile
    private var globalFlowId: String? = null

    /**
     * 生成新的FlowID
     */
    fun generateFlowId(): String {
        return "MOVIE_${System.currentTimeMillis()}"
    }

    /**
     * 设置当前线程的FlowID上下文
     */
    fun setCurrentFlowId(flowId: String?) {
        flowIdContext.set(flowId)
    }

    /**
     * 获取当前线程的FlowID上下文 - 优先返回线程本地，然后全局，最后UNKNOWN
     */
    fun getCurrentFlowId(): String {
        return flowIdContext.get() ?: globalFlowId ?: "UNKNOWN"
    }

    /**
     * 清除当前线程的FlowID上下文
     */
    fun clearCurrentFlowId() {
        flowIdContext.remove()
    }

    /**
     * 设置全局FlowID - 用于跨页面传递
     */
    fun setGlobalFlowId(flowId: String?) {
        globalFlowId = flowId
        Log.d(TAG_MAIN, "🔥 [FlowID全局] 设置全局FlowID: $flowId")
    }

    /**
     * 获取全局FlowID
     */
    fun getGlobalFlowId(): String? {
        return globalFlowId
    }

    /**
     * 清除全局FlowID
     */
    fun clearGlobalFlowId() {
        globalFlowId = null
        Log.d(TAG_MAIN, "🔥 [FlowID全局] 清除全局FlowID")
    }
    
    /**
     * 记录流程开始
     */
    fun logFlowStart(flowId: String, stage: String, message: String) {
        val currentTime = System.currentTimeMillis()
        flowStartTimes[flowId] = currentTime
        
        Log.d(TAG_MAIN, "=== [FlowID:$flowId] === $stage ===")
        Log.d(TAG_MAIN, "[FlowID:$flowId] [${stage.uppercase()}_START] $message")
        Log.d(TAG_SUB, "│ [${timeFormat.format(Date())}] [FlowID:$flowId] [${stage.uppercase()}_START] $message")
    }
    
    /**
     * 记录流程步骤
     */
    fun logFlowStep(flowId: String, step: String, message: String) {
        Log.d(TAG_MAIN, "[FlowID:$flowId] [$step] $message")
        Log.d(TAG_SUB, "│ [${timeFormat.format(Date())}] [FlowID:$flowId] [$step] $message")
    }
    
    /**
     * 记录流程成功
     */
    fun logFlowSuccess(flowId: String, step: String, message: String) {
        val currentTime = System.currentTimeMillis()
        val startTime = flowStartTimes[flowId] ?: currentTime
        val duration = currentTime - startTime
        
        Log.d(TAG_MAIN, "[FlowID:$flowId] [${step}_SUCCESS] $message，耗时: ${duration}ms")
        Log.d(TAG_SUB, "│ [${timeFormat.format(Date())}] [FlowID:$flowId] [${step}_SUCCESS] $message，耗时: ${duration}ms")
    }
    
    /**
     * 记录流程错误
     */
    fun logFlowError(flowId: String, step: String, message: String, error: Throwable? = null) {
        val currentTime = System.currentTimeMillis()
        val startTime = flowStartTimes[flowId] ?: currentTime
        val duration = currentTime - startTime
        
        Log.e(TAG_MAIN, "[FlowID:$flowId] [${step}_ERROR] $message，耗时: ${duration}ms", error)
        Log.e(TAG_SUB, "│ [${timeFormat.format(Date())}] [FlowID:$flowId] [${step}_ERROR] $message", error)
    }
    
    /**
     * 记录流程完成
     */
    fun logFlowComplete(flowId: String, stage: String, message: String) {
        val currentTime = System.currentTimeMillis()
        val startTime = flowStartTimes[flowId] ?: currentTime
        val totalDuration = currentTime - startTime
        
        Log.d(TAG_MAIN, "[FlowID:$flowId] [${stage.uppercase()}_COMPLETE] $message，总耗时: ${totalDuration}ms")
        Log.d(TAG_SUB, "│ [${timeFormat.format(Date())}] [FlowID:$flowId] [${stage.uppercase()}_COMPLETE] $message，总耗时: ${totalDuration}ms")
        Log.d(TAG_MAIN, "=== [FlowID:$flowId] === $stage 完成 ===")
        
        // 清理记录
        flowStartTimes.remove(flowId)
    }
    
    // ===== 搜索阶段日志方法 =====
    
    /**
     * 用户点击电影
     */
    fun logMovieClick(flowId: String, movieName: String, vodId: String, siteKey: String) {
        logFlowStart(flowId, "用户点击电影开始", "点击电影: $movieName")
        logFlowStep(flowId, "MOVIE_CLICK_INFO", "电影信息 - vodId: $vodId, siteKey: $siteKey")
    }
    
    /**
     * 导航到详情页面
     */
    fun logNavigationStart(flowId: String, route: String) {
        logFlowStep(flowId, "NAVIGATION_START", "开始导航到详情页面: $route")
    }
    
    fun logNavigationSuccess(flowId: String) {
        logFlowSuccess(flowId, "NAVIGATION", "导航到详情页面成功")
    }
    
    fun logNavigationError(flowId: String, error: Throwable) {
        logFlowError(flowId, "NAVIGATION", "导航失败", error)
    }
    
    // ===== 解析阶段日志方法 =====
    
    /**
     * 开始获取播放地址
     */
    fun logPlayerContentStart(flowId: String, siteKey: String, flag: String, id: String) {
        logFlowStart(flowId, "开始获取播放地址", "获取播放地址")
        logFlowStep(flowId, "PLAYER_CONTENT_START", "获取播放地址: key=$siteKey, flag=$flag, id=$id")
    }
    
    /**
     * 站点信息
     */
    fun logPlayerContentSite(flowId: String, siteName: String, siteType: Int, api: String) {
        logFlowStep(flowId, "PLAYER_CONTENT_SITE", "站点信息: $siteName (类型:$siteType, API:$api)")
    }
    
    /**
     * Spider类型检查
     */
    fun logSpiderTypeCheck(flowId: String, siteKey: String, api: String, isJS: Boolean, isPY: Boolean, isCSP: Boolean) {
        logFlowStep(flowId, "SPIDER_TYPE_CHECK", "站点 [$siteKey] API: $api, JS: $isJS, PY: $isPY, CSP: $isCSP")
    }
    
    /**
     * Spider创建
     */
    fun logSpiderCreate(flowId: String, siteKey: String, api: String) {
        logFlowStep(flowId, "SPIDER_CREATE", "创建Spider [$siteKey] API: $api")
    }
    
    /**
     * 调用Spider方法
     */
    fun logSpiderCall(flowId: String, siteKey: String, method: String, flag: String, id: String) {
        logFlowStep(flowId, "PLAYER_CONTENT", "调用Spider方法 [$siteKey] $method(flag:$flag, id:$id)")
    }
    
    /**
     * Spider响应
     */
    fun logSpiderResponse(flowId: String, siteKey: String, flag: String, id: String, dataLength: Int) {
        logFlowStep(flowId, "PLAYER_CONTENT", "Spider响应 [$siteKey] flag:$flag id:$id，数据长度: $dataLength")
    }
    
    /**
     * URL解析前后
     */
    fun logUrlBefore(flowId: String, url: String) {
        logFlowStep(flowId, "PLAYER_CONTENT_URL_BEFORE", "解析前URL: $url")
    }
    
    fun logUrlAfter(flowId: String, url: String) {
        logFlowStep(flowId, "PLAYER_CONTENT_URL_AFTER", "解析后URL: $url")
    }
    
    /**
     * 播放地址获取完成
     */
    fun logPlayerContentComplete(flowId: String, url: String, needParse: Boolean) {
        logFlowComplete(flowId, "播放地址获取完成", "URL=$url, 需要解析=$needParse")
    }
    
    // ===== 播放阶段日志方法 =====
    
    /**
     * 播放器开始播放
     */
    fun logPlayerStart(flowId: String, url: String, needParse: Boolean, timeout: Int) {
        logFlowStart(flowId, "播放器开始播放", "播放器开始")
        logFlowStep(flowId, "PLAYER_START", "播放器开始: URL=$url, 需要解析=$needParse, 超时=${timeout}ms")
    }
    
    /**
     * 直接播放
     */
    fun logPlayerDirect(flowId: String, url: String) {
        logFlowStep(flowId, "PLAYER_DIRECT", "直接播放URL: $url")
    }
    
    /**
     * 播放器准备完成
     */
    fun logPlayerReady(flowId: String, url: String) {
        logFlowSuccess(flowId, "PLAYER_READY", "播放器准备完成: $url")
    }
    
    /**
     * 播放开始
     */
    fun logPlaybackStart(flowId: String) {
        logFlowSuccess(flowId, "PLAYBACK_START", "播放开始")
    }
    
    /**
     * 播放完成
     */
    fun logPlaybackComplete(flowId: String) {
        logFlowComplete(flowId, "播放器完成播放", "播放流程全部完成")
    }
}
