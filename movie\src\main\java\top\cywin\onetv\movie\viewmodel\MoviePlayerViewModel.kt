package top.cywin.onetv.movie.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.bean.Flag
import android.util.Log

// ✅ 添加EventBus支持
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.event.*
import top.cywin.onetv.movie.ui.model.*
import top.cywin.onetv.movie.adapter.ViewModelAdapter
import top.cywin.onetv.movie.utils.VodFlowTracker

/**
 * 线路信息 (替代LineManager.LineInfo)
 */
data class LineInfo(
    val flag: String = "",
    val quality: String = "",
    val speed: String = "",
    val isAvailable: Boolean = true
)

/**
 * 播放器UI状态数据类 - 完整版本
 */
data class PlayerUiState(
    // 基础状态
    val isLoading: Boolean = false,
    val error: String? = null,
    val vodId: String = "",
    val siteKey: String = "",

    // 影片信息
    val movie: MovieItem? = null,

    // 播放相关
    val playFlags: List<PlayFlag> = emptyList(),
    val currentFlag: PlayFlag? = null,
    val episodes: List<Episode> = emptyList(),
    val currentEpisode: Episode? = null,
    val currentEpisodeIndex: Int = 0,

    // 播放状态
    val playUrl: String = "",
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0L,
    val duration: Long = 0L,
    val bufferedPosition: Long = 0L,
    val playbackSpeed: Float = 1.0f,
    val volume: Float = 1.0f,

    // UI控制
    val showControls: Boolean = true,
    val isFullscreen: Boolean = false,
    val showSpeedSelector: Boolean = false,
    val showQualitySelector: Boolean = false,

    // 线路信息
    val availableLines: List<LineInfo> = emptyList(),
    val currentLineIndex: Int = 0,

    // WebView解析器
    val showWebViewParser: Boolean = false,
    val webViewParseRequest: WebViewParseRequest? = null,

    // 播放历史
    val watchHistory: WatchHistory? = null
)

/**
 * OneTV Movie播放器ViewModel - 完整版本
 * 处理播放器状态、播放地址解析、播放历史等完整功能
 */
class MoviePlayerViewModel : ViewModel() {

    companion object {
        private const val TAG = "ONETV_MOVIE_PLAYER_VM"
    }

    // ✅ 通过MovieApp访问适配器系统
    private val movieApp = MovieApp.getInstance()
    private val repositoryAdapter = movieApp.repositoryAdapter
    private val viewModelAdapter = movieApp.viewModelAdapter

    private val _uiState = MutableStateFlow(PlayerUiState())
    val uiState: StateFlow<PlayerUiState> = _uiState.asStateFlow()

    // 🔥 FlowID用于全流程跟踪
    private lateinit var currentFlowId: String

    init {
        Log.d(TAG, "🏗️ MoviePlayerViewModel 初始化")

        // 🔥 获取全局FlowID，确保播放器阶段的FlowID连续性
        val globalFlowId = VodFlowTracker.getGlobalFlowId()
        if (globalFlowId != null) {
            currentFlowId = globalFlowId
            VodFlowTracker.setCurrentFlowId(currentFlowId)
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_VM_INIT", "播放器ViewModel初始化，FlowID连续性保持")
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_VM_INIT] 播放器ViewModel初始化完成")
        } else {
            Log.w("VOD_FLOW", "[PLAYER_VM_INIT_WARNING] 未找到全局FlowID，播放器可能无法正确跟踪")
        }

        // ✅ 注册EventBus监听FongMi_TV事件
        EventBus.getDefault().register(this)
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "🧹 MoviePlayerViewModel 清理")

        // ✅ 取消EventBus注册
        try {
            EventBus.getDefault().unregister(this)
        } catch (e: Exception) {
            Log.e(TAG, "EventBus取消注册失败", e)
        }

        // 🔥 清理FlowID上下文
        if (::currentFlowId.isInitialized) {
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_DESTROYED", "播放器ViewModel销毁")
            VodFlowTracker.clearCurrentFlowId()
        }
    }

    // ===== EventBus事件监听 =====

    /**
     * 监听内容详情事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onContentDetail(event: ContentDetailEvent) {
        Log.d(TAG, "📡 收到内容详情事件: success=${event.success}")

        if (event.vod != null && event.success) {
            handleContentDetailSuccess(event.vod)
        } else {
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = event.errorMessage ?: "获取详情失败"
            )
        }
    }

    /**
     * 监听播放地址解析事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlayUrlParse(event: PlayUrlParseEvent) {
        // 🔥 关键修复：添加详细的事件接收日志
        Log.d("VOD_FLOW", "🎯 [PLAYER_EVENT_RECEIVED] MoviePlayerViewModel收到PlayUrlParseEvent")
        Log.d("VOD_FLOW", "🎯 [PLAYER_EVENT_INFO] 事件详情: playUrl=${event.playUrl}, flag=${event.flag}, flowId=${event.flowId}")

        // 🔥 使用事件中的FlowID，确保FlowID连续性
        val eventFlowId = event.flowId ?: (if (::currentFlowId.isInitialized) currentFlowId else null)
        Log.d(TAG, "📡 收到播放地址解析事件: url=${event.playUrl}, FlowID: $eventFlowId")

        if (!event.playUrl.isNullOrEmpty()) {
            // 🔥 第三阶段：播放阶段开始 - 对标原版FongMi_TV
            if (eventFlowId != null) {
                Log.d(TAG, "=== [FlowID:$eventFlowId] === 第三阶段：播放阶段开始 ===")
                VodFlowTracker.logFlowSuccess(eventFlowId, "PLAY_URL_PARSE", "播放地址解析成功: ${event.playUrl}")
                VodFlowTracker.logFlowStep(eventFlowId, "PLAYER_RESULT", "播放地址解析结果: ${event.playUrl}")

                // 🔥 关键：添加播放器启动日志 - 对标原版FongMi_TV
                VodFlowTracker.logFlowStart(eventFlowId, "播放器开始播放", "播放器开始播放")
                VodFlowTracker.logFlowStep(eventFlowId, "PLAYER_START", "播放器启动: ${event.playUrl}")
                VodFlowTracker.logFlowStep(eventFlowId, "PLAYER_DIRECT", "直接播放URL: ${event.playUrl}")
            }
            handlePlayUrlParseSuccess(event.playUrl, event.headers)
        } else {
            // 🔥 记录播放地址解析失败
            if (eventFlowId != null) {
                VodFlowTracker.logFlowError(eventFlowId, "PLAY_URL_PARSE", "播放地址解析失败")
            }
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "播放地址解析失败"
            )
        }
    }

    /**
     * 监听WebView解析事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWebViewParseSuccess(event: WebViewParseSuccessEvent) {
        Log.d(TAG, "📡 收到WebView解析成功事件: url=${event.playUrl}")

        handlePlayUrlParseSuccess(event.playUrl, event.headers)
    }

    /**
     * 监听WebView解析错误事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWebViewParseError(event: WebViewParseErrorEvent) {
        Log.e(TAG, "📡 收到WebView解析错误事件: ${event.error}")

        _uiState.value = _uiState.value.copy(
            isLoading = false,
            error = "WebView解析失败: ${event.error}"
        )
    }

    /**
     * 监听错误事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onError(event: ErrorEvent) {
        Log.e(TAG, "📡 收到错误事件: ${event.msg}")

        _uiState.value = _uiState.value.copy(
            isLoading = false,
            error = event.msg
        )
    }

    // ===== 公共方法 =====

    /**
     * 初始化播放器
     */
    fun initPlayer(vodId: String, siteKey: String, episodeIndex: Int = 0, flowId: String? = null) {
        viewModelScope.launch {
            try {
                // 🔥 关键修复：使用传入的FlowID，确保连续性
                val flowIdToUse = flowId ?: VodFlowTracker.generateFlowId()
                currentFlowId = flowIdToUse
                VodFlowTracker.setCurrentFlowId(flowIdToUse)

                if (flowId != null) {
                    Log.d(TAG, "🔥 [FlowID修复] 播放器使用传入的FlowID: $flowIdToUse")
                    VodFlowTracker.logFlowStep(flowIdToUse, "PLAYER_INIT_CONTINUE", "播放器继续使用现有FlowID: vodId=$vodId, episodeIndex=$episodeIndex")

                    // 🔥 关键修复：如果有FlowID传入，说明是自动播放模式，不需要重新获取详情
                    // 直接等待PlayUrlParseEvent事件即可
                    Log.d(TAG, "🔥 [自动播放模式] 播放器等待PlayUrlParseEvent事件，不重新获取详情")
                    _uiState.value = _uiState.value.copy(
                        isLoading = true,
                        error = null,
                        vodId = vodId,
                        siteKey = siteKey,
                        currentEpisodeIndex = episodeIndex
                    )
                    VodFlowTracker.logFlowStep(flowIdToUse, "PLAYER_WAIT_EVENT", "播放器等待播放地址解析事件")
                } else {
                    Log.d(TAG, "🔥 [FlowID修复] 播放器生成新的FlowID: $flowIdToUse")
                    VodFlowTracker.logFlowStart(flowIdToUse, "播放器初始化", "初始化播放器: vodId=$vodId, episodeIndex=$episodeIndex")

                    // 🔥 手动播放模式：需要重新获取详情
                    Log.d(TAG, "▶️ 初始化播放器: vodId=$vodId, episodeIndex=$episodeIndex, FlowID=$flowIdToUse")
                    _uiState.value = _uiState.value.copy(
                        isLoading = true,
                        error = null,
                        vodId = vodId,
                        siteKey = siteKey,
                        currentEpisodeIndex = episodeIndex
                    )

                    // ✅ 通过适配器获取影片详情
                    VodFlowTracker.logFlowStep(flowIdToUse, "DETAIL_REQUEST", "请求获取影片详情")
                    repositoryAdapter.getContentDetail(vodId, siteKey)
                }

            } catch (e: Exception) {
                Log.e(TAG, "💥 播放器初始化失败", e)
                if (::currentFlowId.isInitialized) {
                    VodFlowTracker.logFlowError(currentFlowId, "PLAYER_INIT", "播放器初始化失败", e)
                }
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "播放器初始化失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 播放指定剧集
     */
    fun playEpisode(episode: Episode, episodeIndex: Int = 0) {
        val currentState = _uiState.value
        val movie = currentState.movie
        val currentFlag = currentState.currentFlag

        if (movie == null || currentFlag == null) {
            _uiState.value = _uiState.value.copy(error = "播放器未初始化")
            return
        }

        viewModelScope.launch {
            try {
                // 🔥 如果没有FlowID，生成一个新的
                if (!::currentFlowId.isInitialized) {
                    currentFlowId = VodFlowTracker.generateFlowId()
                }

                VodFlowTracker.logFlowStep(currentFlowId, "EPISODE_PLAY_START", "开始播放剧集: ${episode.name}")
                Log.d(TAG, "📺 播放剧集: ${episode.name}")

                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    currentEpisode = episode,
                    currentEpisodeIndex = episodeIndex
                )

                // ✅ 通过适配器解析播放地址
                VodFlowTracker.logFlowStep(currentFlowId, "PARSE_PLAY_URL", "解析播放地址: url=${episode.url}, flag=${currentFlag.flag}")
                VodFlowTracker.setCurrentFlowId(currentFlowId) // 设置FlowID上下文
                repositoryAdapter.parsePlayUrl(episode.url, movie.siteKey, currentFlag.flag)

                // 播放地址将通过事件回调处理

            } catch (e: Exception) {
                Log.e(TAG, "💥 剧集播放失败", e)
                if (::currentFlowId.isInitialized) {
                    VodFlowTracker.logFlowError(currentFlowId, "EPISODE_PLAY", "剧集播放失败", e)
                }
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "剧集播放失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 播放下一集
     */
    fun playNextEpisode() {
        val currentState = _uiState.value
        val episodes = currentState.episodes
        val currentIndex = currentState.currentEpisodeIndex

        if (currentIndex < episodes.size - 1) {
            val nextEpisode = episodes[currentIndex + 1]
            playEpisode(nextEpisode, currentIndex + 1)
            Log.d("ONETV_MOVIE", "⏭️ 播放下一集: ${nextEpisode.name}")
        } else {
            Log.d("ONETV_MOVIE", "⚠️ 已经是最后一集")
        }
    }

    /**
     * 播放上一集
     */
    fun playPreviousEpisode() {
        val currentState = _uiState.value
        val episodes = currentState.episodes
        val currentIndex = currentState.currentEpisodeIndex

        if (currentIndex > 0) {
            val previousEpisode = episodes[currentIndex - 1]
            playEpisode(previousEpisode, currentIndex - 1)
            Log.d("ONETV_MOVIE", "⏮️ 播放上一集: ${previousEpisode.name}")
        } else {
            Log.d("ONETV_MOVIE", "⚠️ 已经是第一集")
        }
    }

    /**
     * 选择播放源
     */
    fun selectFlag(flag: Flag) {
        Log.d("ONETV_MOVIE", "🎬 选择播放源: ${flag.flag}")

        // 解析剧集列表
        val episodes = parseEpisodes(flag.urls)
        val defaultEpisode = episodes.firstOrNull()

        if (defaultEpisode != null) {
            // ✅ 转换Flag为PlayFlag
            val playFlag = ViewModelAdapter.convertFlagToPlayFlag(flag)

            _uiState.value = _uiState.value.copy(
                currentFlag = playFlag,
                episodes = episodes,
                currentEpisode = defaultEpisode,
                currentEpisodeIndex = 0
            )

            // 播放第一集
            playEpisode(defaultEpisode, 0)
        }
    }

    /**
     * 解析剧集列表
     */
    private fun parseEpisodes(urls: String): List<Episode> {
        return try {
            urls.split("#").mapIndexed { index, episodeData ->
                val parts = episodeData.split("$")
                Episode(
                    index = index,
                    name = if (parts.size >= 2) parts[0] else "第${index + 1}集",
                    url = if (parts.size >= 2) parts[1] else episodeData,
                    isWatched = false,
                    progress = 0f
                )
            }
        } catch (e: Exception) {
            Log.e("ONETV_MOVIE", "剧集解析失败", e)
            emptyList()
        }
    }

    /**
     * 选择剧集
     */
    fun selectEpisode(episode: Episode) {
        val currentState = _uiState.value
        val episodes = currentState.episodes
        val episodeIndex = episodes.indexOf(episode)

        if (episodeIndex >= 0) {
            playEpisode(episode, episodeIndex)
        }
    }

    /**
     * 更新播放进度
     */
    fun updatePlayProgress(position: Long, duration: Long) {
        _uiState.value = _uiState.value.copy(
            currentPosition = position,
            duration = duration
        )

        // ✅ 通过适配器保存播放历史 - 历史管理在FongMi_TV中
        val currentState = _uiState.value
        val movie = currentState.movie
        val episode = currentState.currentEpisode

        if (movie != null && episode != null) {
            repositoryAdapter.savePlayHistory(
                movie.vodId,
                movie.siteKey,
                episode.index,
                position,
                0L // duration参数
            )
        }
    }


    /**
     * 切换到指定线路
     */
    fun switchToLine(lineInfo: LineInfo) {
        Log.d("ONETV_MOVIE", "🔄 切换线路: ${lineInfo.flag}")

        val currentState = _uiState.value
        val currentEpisode = currentState.currentEpisode

        if (currentEpisode != null) {
            // ✅ 通过适配器切换线路 - 线路管理在FongMi_TV中
            repositoryAdapter.switchLine(lineInfo.flag, currentEpisode.url)

            val lineIndex = currentState.availableLines.indexOf(lineInfo)
            _uiState.value = _uiState.value.copy(
                currentLineIndex = lineIndex.coerceAtLeast(0)
            )
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 设置播放状态
     */
    fun setPlayingState(isPlaying: Boolean) {
        _uiState.value = _uiState.value.copy(isPlaying = isPlaying)
    }

    /**
     * 切换播放/暂停状态
     */
    fun togglePlayPause() {
        val currentState = _uiState.value
        val newPlayingState = !currentState.isPlaying

        _uiState.value = _uiState.value.copy(isPlaying = newPlayingState)

        // ✅ 通过EventBus发送播放控制事件
        if (newPlayingState) {
            EventBus.getDefault().post(ActionEvent(ActionEvent.PLAY))
        } else {
            EventBus.getDefault().post(ActionEvent(ActionEvent.PAUSE))
        }

        Log.d(TAG, if (newPlayingState) "▶️ 播放" else "⏸️ 暂停")
    }

    /**
     * 跳转到指定位置
     */
    fun seekTo(position: Long) {
        _uiState.value = _uiState.value.copy(currentPosition = position)

        // ✅ 通过EventBus发送播放器控制事件 - 跟随FongMi_TV架构
        try {
            // 使用PlayerEvent发送跳转事件
            EventBus.getDefault().post(PlayerEvent.state("seek", position.toInt()))
        } catch (e: Exception) {
            Log.e(TAG, "跳转失败", e)
        }

        Log.d(TAG, "⏩ 跳转到位置: ${position}ms")
    }

    /**
     * 更新播放进度
     */
    fun updatePlaybackPosition(position: Long, duration: Long) {
        _uiState.value = _uiState.value.copy(
            currentPosition = position,
            duration = duration
        )

        // ✅ 保存播放历史
        savePlaybackHistory(position, duration)
    }

    /**
     * WebView解析成功回调
     */
    fun onWebViewParseSuccess(playUrl: String) {
        Log.d(TAG, "✅ WebView解析成功: $playUrl")
        handlePlayUrlParseSuccess(playUrl, null)
    }

    /**
     * WebView解析失败回调
     */
    fun onWebViewParseError(error: String) {
        Log.e(TAG, "❌ WebView解析失败: $error")
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            error = "解析失败: $error",
            showWebViewParser = false,
            webViewParseRequest = null
        )
    }

    // ===== 私有方法 =====

    /**
     * 处理内容详情成功
     */
    private fun handleContentDetailSuccess(vod: top.cywin.onetv.movie.bean.Vod) {
        Log.d(TAG, "✅ 处理内容详情成功: ${vod.vodName}")

        try {
            // ✅ 转换为UI模型
            val movieItem = ViewModelAdapter.convertVodToMovie(vod)
            if (movieItem == null) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "数据转换失败"
                )
                return
            }

            // ✅ 解析播放源
            val playFlags = ViewModelAdapter.convertVodFlags(vod)
            if (playFlags.isEmpty()) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "没有找到播放源"
                )
                return
            }

            // ✅ 选择默认播放源和剧集
            val defaultFlag = playFlags.firstOrNull()
            val episodes = if (defaultFlag != null) {
                ViewModelAdapter.convertVodEpisodes(defaultFlag.urls)
            } else {
                emptyList()
            }

            if (episodes.isEmpty()) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "没有找到可播放的剧集"
                )
                return
            }

            // ✅ 根据指定的剧集索引选择剧集
            val currentState = _uiState.value
            val targetEpisodeIndex = currentState.currentEpisodeIndex.coerceIn(0, episodes.size - 1)
            val targetEpisode = episodes.getOrNull(targetEpisodeIndex) ?: episodes.first()

            // ✅ 更新UI状态
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                movie = movieItem,
                playFlags = playFlags,
                currentFlag = defaultFlag,
                episodes = episodes,
                currentEpisode = targetEpisode,
                currentEpisodeIndex = targetEpisodeIndex,
                error = null
            )

            // ✅ 自动开始播放指定剧集
            playEpisode(targetEpisode, targetEpisodeIndex)

        } catch (e: Exception) {
            Log.e(TAG, "💥 详情处理失败", e)
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "详情处理失败: ${e.message}"
            )
        }
    }

    /**
     * 处理播放地址解析成功
     */
    private fun handlePlayUrlParseSuccess(playUrl: String, headers: Map<String, String>?) {
        Log.d(TAG, "✅ 播放地址解析成功: $playUrl")

        // 🔥 第三阶段：播放器启动日志 - 对标原版FongMi_TV
        if (::currentFlowId.isInitialized) {
            VodFlowTracker.logPlayerStart(currentFlowId, playUrl, false, 30000)
            VodFlowTracker.logPlayerDirect(currentFlowId, playUrl)
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_START", "播放器启动: $playUrl")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_PREPARED", "播放器准备完成")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_STARTED", "播放器开始播放")
        }

        // 🔥 关键修复：立即更新UI状态，停止显示"正在解析播放地址"
        _uiState.value = _uiState.value.copy(
            isLoading = false,  // 🔥 关键：停止加载状态
            playUrl = playUrl,
            error = null,
            isPlaying = true,   // 🔥 自动开始播放
            showWebViewParser = false  // 🔥 隐藏WebView解析器
        )

        Log.d(TAG, "🎬 UI状态已更新: isLoading=false, playUrl=$playUrl")

        // 🔥 记录播放器准备完成 - 等待真实播放器事件
        if (::currentFlowId.isInitialized) {
            VodFlowTracker.logPlaybackStart(currentFlowId)
            VodFlowTracker.logPlayerReady(currentFlowId, playUrl)
        }
    }

    /**
     * 保存播放历史
     */
    private fun savePlaybackHistory(position: Long, duration: Long) {
        val currentState = _uiState.value
        val movie = currentState.movie
        val episode = currentState.currentEpisode

        if (movie != null && episode != null && duration > 0) {
            viewModelScope.launch {
                try {
                    repositoryAdapter.saveWatchHistory(
                        movie.vodId,
                        movie.vodName,
                        position,
                        duration
                    )
                } catch (e: Exception) {
                    // 历史保存失败不影响播放
                    Log.e(TAG, "💥 保存播放历史失败", e)
                }
            }
        }
    }

    /**
     * 播放器状态回调 - 供播放器组件调用
     */
    fun onPlayerStateChanged(state: String, position: Long = 0, duration: Long = 0) {
        if (::currentFlowId.isInitialized) {
            when (state) {
                "INITIALIZED" -> {
                    VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_INITIALIZED", "播放器初始化完成")
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INITIALIZED] 播放器初始化完成")
                }
                "PREPARED" -> {
                    VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_PREPARED", "播放器准备完成")
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_PREPARED] 播放器准备完成，准备开始播放")
                }
                "STARTED" -> {
                    VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_STARTED", "播放器开始播放")
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_STARTED] 播放器开始播放，position=$position, duration=$duration")
                }
                "PAUSED" -> {
                    VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_PAUSED", "播放器暂停")
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_PAUSED] 播放器暂停，position=$position")
                }
                "COMPLETED" -> {
                    VodFlowTracker.logPlaybackComplete(currentFlowId)
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_COMPLETED] 播放完成")
                }
                "ERROR" -> {
                    VodFlowTracker.logFlowError(currentFlowId, "PLAYER_ERROR", "播放器发生错误")
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_ERROR] 播放器发生错误")
                }
                "POSITION_UPDATE" -> {
                    // 定期更新播放进度，不记录到FlowTracker避免日志过多
                    Log.v("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_PROGRESS] 播放进度更新: $position/$duration")
                }
                "DURATION_UPDATE" -> {
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_DURATION] 视频时长: $duration")
                }
            }
        } else {
            Log.w("VOD_FLOW", "[PLAYER_STATE_WARNING] FlowID未初始化，播放器状态: $state")
        }
        Log.d(TAG, "🎬 播放器状态变化: $state, position=$position, duration=$duration")
    }

    /**
     * 重试播放 - 供UI调用
     */
    fun retryPlay() {
        val currentState = _uiState.value
        if (currentState.vodId.isNotEmpty() && currentState.siteKey.isNotEmpty()) {
            // 🔥 确保FlowID连续性 - 优先使用当前FlowID，然后全局FlowID
            val flowIdToUse = if (::currentFlowId.isInitialized) {
                currentFlowId
            } else {
                VodFlowTracker.getGlobalFlowId() ?: VodFlowTracker.generateFlowId().also {
                    currentFlowId = it
                    VodFlowTracker.setGlobalFlowId(it)
                }
            }

            Log.d(TAG, "🔄 [FlowID:$flowIdToUse] 重试播放: vodId=${currentState.vodId}, episodeIndex=${currentState.currentEpisodeIndex}")
            VodFlowTracker.logFlowStep(flowIdToUse, "PLAYER_RETRY", "用户点击重试播放")

            // 清除错误状态，重新初始化
            _uiState.value = currentState.copy(
                error = null,
                isLoading = true
            )

            // 重新初始化播放器，使用相同的FlowID
            initPlayer(currentState.vodId, currentState.siteKey, currentState.currentEpisodeIndex, flowIdToUse)
        }
    }

    /**
     * 播放进度回调 - 供播放器组件调用
     */
    fun onPlayerProgress(position: Long, duration: Long) {
        // 可以在这里记录播放进度，但不需要每次都记录日志
        // 只在关键节点记录，比如播放到50%时
        if (duration > 0) {
            val progress = (position * 100 / duration).toInt()
            if (progress == 50 && ::currentFlowId.isInitialized) {
                VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_PROGRESS_50", "播放进度达到50%")
            }
        }
    }

}
