package top.cywin.onetv.movie.navigation

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import top.cywin.onetv.movie.ui.screens.MovieHomeScreen
import top.cywin.onetv.movie.ui.screens.MovieCategoryScreen
import top.cywin.onetv.movie.ui.screens.MovieDetailScreen
import top.cywin.onetv.movie.ui.screens.MoviePlayerScreen
import top.cywin.onetv.movie.ui.screens.MovieSearchScreen
import top.cywin.onetv.movie.ui.screens.MovieSearchResultListScreen
import top.cywin.onetv.movie.ui.screens.MovieHistoryScreen
import top.cywin.onetv.movie.ui.screens.MovieSettingsScreen
import top.cywin.onetv.movie.ui.screens.MovieConfigScreen
import android.util.Log

/**
 * Movie模块导航扩展 (TVBOX标准)
 */
fun NavGraphBuilder.movieNavigation(navController: NavController) {
    
    // 点播首页
    composable("movie_home") {
        // ✅ 使用LaunchedEffect确保日志只在首次组合时执行
        LaunchedEffect(Unit) {
            Log.d("ONETV_MOVIE_NAV", "🎬 [第3阶段] 进入Movie模块导航路由")
            Log.d("ONETV_MOVIE_NAV", "📍 位置: MovieNavigation.kt:31")
            Log.d("ONETV_MOVIE_NAV", "🎯 路由: movie_home")
            Log.d("ONETV_MOVIE_NAV", "⏰ 时间戳: ${System.currentTimeMillis()}")
            Log.d("ONETV_MOVIE_NAV", "🚀 [第3阶段] 开始创建MovieHomeScreen组件")
        }

        MovieHomeScreen(navController = navController)

        // ✅ 组件创建完成的日志也放在LaunchedEffect中
        LaunchedEffect(Unit) {
            Log.d("ONETV_MOVIE_NAV", "✅ [第3阶段] MovieHomeScreen组件创建完成")
        }
    }
    
    // 分类页面
    composable(
        "movie_category/{typeId}",
        arguments = listOf(navArgument("typeId") { type = NavType.StringType })
    ) { backStackEntry ->
        val typeId = backStackEntry.arguments?.getString("typeId") ?: ""
        MovieCategoryScreen(
            typeId = typeId,
            navController = navController
        )
    }
    
    // 分类页面（带站点参数）
    composable(
        "movie_category/{typeId}/{siteKey}",
        arguments = listOf(
            navArgument("typeId") { type = NavType.StringType },
            navArgument("siteKey") { type = NavType.StringType }
        )
    ) { backStackEntry ->
        val typeId = backStackEntry.arguments?.getString("typeId") ?: ""
        val siteKey = backStackEntry.arguments?.getString("siteKey") ?: ""
        MovieCategoryScreen(
            typeId = typeId,
            siteKey = siteKey,
            navController = navController
        )
    }
    
    // 详情页面
    composable(
        "movie_detail/{vodId}",
        arguments = listOf(navArgument("vodId") { type = NavType.StringType })
    ) { backStackEntry ->
        val vodId = backStackEntry.arguments?.getString("vodId") ?: ""
        MovieDetailScreen(
            vodId = vodId,
            navController = navController
        )
    }

    // 详情页面（带站点参数）
    composable(
        "movie_detail/{vodId}/{siteKey}",
        arguments = listOf(
            navArgument("vodId") { type = NavType.StringType },
            navArgument("siteKey") { type = NavType.StringType }
        )
    ) { backStackEntry ->
        val vodId = backStackEntry.arguments?.getString("vodId") ?: ""
        val siteKey = backStackEntry.arguments?.getString("siteKey") ?: ""
        MovieDetailScreen(
            vodId = vodId,
            siteKey = siteKey,
            navController = navController
        )
    }
    
    // 详情页面（带站点参数）
    composable(
        "movie_detail/{vodId}/{siteKey}",
        arguments = listOf(
            navArgument("vodId") { type = NavType.StringType },
            navArgument("siteKey") { type = NavType.StringType }
        )
    ) { backStackEntry ->
        val vodId = backStackEntry.arguments?.getString("vodId") ?: ""
        val siteKey = backStackEntry.arguments?.getString("siteKey") ?: ""
        MovieDetailScreen(
            vodId = vodId,
            siteKey = siteKey,
            navController = navController
        )
    }


    
    // 播放页面
    composable(
        "movie_player/{vodId}/{episodeIndex}",
        arguments = listOf(
            navArgument("vodId") { type = NavType.StringType },
            navArgument("episodeIndex") { type = NavType.IntType }
        )
    ) { backStackEntry ->
        val vodId = backStackEntry.arguments?.getString("vodId") ?: ""
        val episodeIndex = backStackEntry.arguments?.getInt("episodeIndex") ?: 0
        // TODO: 需要传递flag和episode参数，暂时使用默认值
        val defaultFlag = top.cywin.onetv.movie.bean.Flag().apply {
            setFlag("默认")
        }
        val defaultEpisode = top.cywin.onetv.movie.ui.model.Episode(
            index = episodeIndex,
            name = "第${episodeIndex + 1}集",
            url = ""
        )
        MoviePlayerScreen(
            navController = navController,
            siteKey = "",
            vodId = vodId,
            flag = defaultFlag,
            episode = defaultEpisode
        )
    }
    
    // 播放页面（带站点参数）
    composable(
        "movie_player/{vodId}/{episodeIndex}/{siteKey}",
        arguments = listOf(
            navArgument("vodId") { type = NavType.StringType },
            navArgument("episodeIndex") { type = NavType.IntType },
            navArgument("siteKey") { type = NavType.StringType }
        )
    ) { backStackEntry ->
        val vodId = backStackEntry.arguments?.getString("vodId") ?: ""
        val episodeIndex = backStackEntry.arguments?.getInt("episodeIndex") ?: 0
        val siteKey = backStackEntry.arguments?.getString("siteKey") ?: ""
        // TODO: 需要传递flag和episode参数，暂时使用默认值
        val defaultFlag = top.cywin.onetv.movie.bean.Flag().apply {
            setFlag("默认")
        }
        val defaultEpisode = top.cywin.onetv.movie.ui.model.Episode(
            index = episodeIndex,
            name = "第${episodeIndex + 1}集",
            url = ""
        )
        MoviePlayerScreen(
            navController = navController,
            siteKey = siteKey,
            vodId = vodId,
            flag = defaultFlag,
            episode = defaultEpisode
        )
    }
    
    // 搜索页面
    composable("movie_search") {
        MovieSearchScreen(navController = navController)
    }
    
    // 搜索页面（带关键词）
    composable(
        "movie_search/{keyword}",
        arguments = listOf(navArgument("keyword") { type = NavType.StringType })
    ) { backStackEntry ->
        val keyword = backStackEntry.arguments?.getString("keyword") ?: ""
        MovieSearchScreen(
            initialKeyword = keyword,
            navController = navController
        )
    }

    // 搜索结果列表页面
    composable(
        "movie_search_result_list/{keyword}",
        arguments = listOf(navArgument("keyword") { type = NavType.StringType })
    ) { backStackEntry ->
        val keyword = backStackEntry.arguments?.getString("keyword") ?: ""
        MovieSearchResultListScreen(
            keyword = keyword,
            navController = navController
        )
    }
    
    // 历史记录页面
    composable("movie_history") {
        MovieHistoryScreen(navController = navController)
    }
    
    // 设置页面
    composable("movie_settings") {
        MovieSettingsScreen(navController = navController)
    }

    // 配置页面
    composable("movie_config") {
        MovieConfigScreen(navController = navController)
    }
}

/**
 * Movie导航路由常量
 */
object MovieRoutes {
    const val HOME = "movie_home"
    const val CATEGORY = "movie_category"
    const val DETAIL = "movie_detail"
    const val PLAYER = "movie_player"
    const val SEARCH = "movie_search"
    const val SEARCH_RESULT_LIST = "movie_search_result_list"
    const val HISTORY = "movie_history"
    const val SETTINGS = "movie_settings"
    const val CONFIG = "movie_config"
    
    /**
     * 构建分类路由
     */
    fun category(typeId: String, siteKey: String? = null): String {
        return if (siteKey != null) {
            "$CATEGORY/$typeId/$siteKey"
        } else {
            "$CATEGORY/$typeId"
        }
    }
    
    /**
     * 构建详情路由 - 完全按照原版FongMi_TV实现
     */
    fun detail(vodId: String, siteKey: String? = null): String {
        // 对vodId进行URL编码，处理特殊字符
        val encodedVodId = try {
            java.net.URLEncoder.encode(vodId, "UTF-8")
        } catch (e: Exception) {
            vodId // 编码失败时使用原始ID
        }

        return if (siteKey != null && siteKey.isNotEmpty()) {
            val encodedSiteKey = try {
                java.net.URLEncoder.encode(siteKey, "UTF-8")
            } catch (e: Exception) {
                siteKey
            }
            "$DETAIL/$encodedVodId/$encodedSiteKey"
        } else {
            "$DETAIL/$encodedVodId"
        }
    }
    
    /**
     * 构建播放路由
     */
    fun player(vodId: String, episodeIndex: Int, siteKey: String? = null): String {
        return if (siteKey != null) {
            "$PLAYER/$vodId/$episodeIndex/$siteKey"
        } else {
            "$PLAYER/$vodId/$episodeIndex"
        }
    }
    
    /**
     * 构建搜索路由
     */
    fun search(keyword: String? = null): String {
        return if (keyword != null) {
            "$SEARCH/$keyword"
        } else {
            SEARCH
        }
    }

    /**
     * 构建搜索结果列表路由
     */
    fun searchResultList(keyword: String): String {
        return "$SEARCH_RESULT_LIST/$keyword"
    }
}
