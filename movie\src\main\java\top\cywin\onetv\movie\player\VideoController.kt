package top.cywin.onetv.movie.player

import android.app.Activity
import android.util.Log
import android.os.Handler
import android.os.Looper
import androidx.compose.runtime.*
import top.cywin.onetv.movie.Setting
import top.cywin.onetv.movie.bean.History
import top.cywin.onetv.movie.bean.Track
import top.cywin.onetv.movie.bean.Result
import top.cywin.onetv.movie.utils.VodFlowTracker

/**
 * 播放器控制器 - 直接移植原版控制逻辑
 * 基于：FongMi_TV/src/leanback/java/top/cywin/onetv/vod/ui/activity/VideoActivity.java
 */
class VideoController(private val activity: Activity) {
    private var _mPlayers: Players? = null
    private var mHistory: History? = null

    // 播放器状态
    var isInitialized by mutableStateOf(false)
        private set

    // 公共访问播放器实例
    val mPlayers: Players? get() = _mPlayers
    
    fun initialize(): Players? {
        // 🔥 添加VOD_FLOW日志跟踪播放器初始化
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"

        if (_mPlayers == null) {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT] 播放器初始化开始")
            _mPlayers = Players.create(activity)

            // 🔥 关键修复：设置VideoController引用，支持播放中断处理
            _mPlayers?.setVideoController(this)

            isInitialized = true
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT_SUCCESS] 播放器初始化完成")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_INIT", "播放器初始化完成")
        } else {
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_INIT_REUSE] 复用现有播放器实例")
        }
        return _mPlayers
    }
    
    fun setHistory(history: History) {
        mHistory = history
    }
    
    // 直接移植原版方法：onScale()
    fun onScale(): String {
        val index = getScale()
        val scaleArray = arrayOf("默认", "16:9", "4:3", "填充", "原始", "裁剪")
        val newIndex = if (index == scaleArray.size - 1) 0 else index + 1
        mHistory?.setScale(newIndex)
        Setting.putScale(newIndex)
        setScale(newIndex)
        return scaleArray[newIndex]
    }
    
    private fun getScale(): Int {
        return Setting.getScale()
    }
    
    private fun setScale(index: Int) {
        // 直接移植原版setScale逻辑
        _mPlayers?.setVideoScale(index)
    }

    // 直接移植原版方法：onSpeed() - 优化版本
    fun onSpeed(): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_SPEED] 播放速度调整")

        return safePlayerOperation("1.00") { players ->
            val result = players.addSpeed()
            mHistory?.setSpeed(players.speed)
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_SPEED_SUCCESS] 播放速度调整为: $result")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_SPEED", "播放速度调整为: $result")
            result
        }
    }

    // 直接移植原版方法：onSpeedAdd()
    fun onSpeedAdd(): String {
        val result = _mPlayers?.addSpeed(0.25f) ?: "1.00"
        mHistory?.setSpeed(_mPlayers?.getSpeed() ?: 1.0f)
        return result
    }

    // 直接移植原版方法：onSpeedSub()
    fun onSpeedSub(): String {
        val result = _mPlayers?.subSpeed(0.25f) ?: "1.00"
        mHistory?.setSpeed(_mPlayers?.getSpeed() ?: 1.0f)
        return result
    }

    // 直接移植原版方法：onSpeedLong()
    fun onSpeedLong(): String {
        val result = _mPlayers?.toggleSpeed() ?: "1.00"
        mHistory?.setSpeed(_mPlayers?.getSpeed() ?: 1.0f)
        return result
    }

    // 直接移植原版方法：onDecode()
    fun onDecode(): String {
        _mPlayers?.toggleDecode()
        setDecode()
        return _mPlayers?.decodeText ?: "软解"
    }
    
    private fun setDecode() {
        // 直接移植原版setDecode逻辑
        // 原版会更新UI显示当前解码模式
    }
    
    // 直接移植原版方法：onLoop()
    fun onLoop(): String {
        val isActivated = !(mHistory?.isLoop() ?: false)
        mHistory?.setLoop(isActivated)
        return if (isActivated) "循环开" else "循环关"
    }
    
    // 直接移植原版方法：onOpening()
    fun onOpening(): String {
        val current = _mPlayers?.getPosition() ?: 0
        val duration = _mPlayers?.getDuration() ?: 0
        if (current < 0 || duration < 0) return "片头"
        if (current > 60000) return "片头" // OPED_LIMIT = 60秒
        setOpening(current)
        return formatTime(mHistory?.getOpening() ?: 0)
    }

    // 直接移植原版方法：onOpeningAdd()
    fun onOpeningAdd(): String {
        val currentOpening = mHistory?.getOpening() ?: 0
        setOpening(maxOf(0, currentOpening + 1000))
        return formatTime(mHistory?.getOpening() ?: 0)
    }

    // 直接移植原版方法：onOpeningSub()
    fun onOpeningSub(): String {
        val currentOpening = mHistory?.getOpening() ?: 0
        setOpening(maxOf(0, currentOpening - 1000))
        return formatTime(mHistory?.getOpening() ?: 0)
    }

    private fun setOpening(opening: Long) {
        mHistory?.setOpening(opening)
    }

    // 直接移植原版方法：onEnding()
    fun onEnding(): String {
        val current = _mPlayers?.getPosition() ?: 0
        val duration = _mPlayers?.getDuration() ?: 0
        if (current < 0 || duration < 0) return "片尾"
        if (duration - current > 60000) return "片尾" // OPED_LIMIT = 60秒
        setEnding(duration - current)
        return formatTime(mHistory?.getEnding() ?: 0)
    }
    
    // 直接移植原版方法：onEndingAdd()
    fun onEndingAdd(): String {
        val currentEnding = mHistory?.getEnding() ?: 0
        setEnding(maxOf(0, currentEnding + 1000))
        return formatTime(mHistory?.getEnding() ?: 0)
    }
    
    // 直接移植原版方法：onEndingSub()
    fun onEndingSub(): String {
        val currentEnding = mHistory?.getEnding() ?: 0
        setEnding(maxOf(0, currentEnding - 1000))
        return formatTime(mHistory?.getEnding() ?: 0)
    }
    
    private fun setEnding(ending: Long) {
        mHistory?.setEnding(ending)
    }
    

    
    // 直接移植原版方法：onChoose() - 播放器切换
    fun onPlayer(): String {
        _mPlayers?.choose(activity, "播放器选择")
        return "EXO"
    }

    // 直接移植原版方法：onReset()
    fun onReset(): String {
        onReset(isReplay())
        return "刷新"
    }

    // 直接移植原版方法：onRefresh()
    fun onRefresh(): String {
        onReset(false)
        return "刷新"
    }

    private fun onReset(replay: Boolean) {
        _mPlayers?.stop()
        _mPlayers?.clear()
        // 原版会重新获取播放地址
    }
    
    private fun isReplay(): Boolean {
        return Setting.getReset() == 1
    }
    
    // 直接移植原版方法：onResetToggle()
    fun onResetToggle(): String {
        val resetModes = arrayOf("刷新", "重载")
        val currentMode = Setting.getReset()
        val newMode = Math.abs(currentMode - 1)
        Setting.putReset(newMode)
        return resetModes[newMode]
    }
    
    // 换源功能
    fun onChangeSource(): String {
        // 原版onChange逻辑
        return "换源"
    }
    
    // 直接移植原版轨道选择功能 - 优化版本
    fun onTrack(trackType: Int): String {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        val defaultName = when (trackType) {
            1 -> "音轨"
            2 -> "视轨"
            3 -> "字幕"
            else -> "轨道"
        }

        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_TRACK] 轨道切换: $defaultName")

        return safePlayerOperation(defaultName) { players ->
            val key = players.key ?: return@safePlayerOperation defaultName
            val tracks = top.cywin.onetv.movie.bean.Track.find(key)

            // 找到指定类型的轨道
            val typeTracks = tracks.filter { it.type == trackType }
            if (typeTracks.isEmpty()) {
                return@safePlayerOperation defaultName
            }

            // 找到当前选中的轨道
            val currentIndex = typeTracks.indexOfFirst { it.isSelected }
            val nextIndex = if (currentIndex == -1) 0 else (currentIndex + 1) % typeTracks.size

            // 取消所有同类型轨道的选择
            typeTracks.forEach { it.setSelected(false) }

            // 选择下一个轨道
            val selectedTrack = typeTracks[nextIndex]
            selectedTrack.setSelected(true)

            // 应用轨道选择
            players.setTrack(listOf(selectedTrack))

            val trackName = selectedTrack.name ?: defaultName
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_TRACK_SUCCESS] 轨道切换成功: $trackName")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_TRACK", "轨道切换成功: $trackName")

            trackName
        }
    }

    fun onTextTrack(): String = onTrack(3)
    fun onAudioTrack(): String = onTrack(1)
    fun onVideoTrack(): String = onTrack(2)

    // 直接移植原版弹幕功能
    fun onDanmaku(): String {
        val isShow = !Setting.isDanmakuShow()
        Setting.putDanmakuShow(isShow)
        // 原版通过DanPlayer控制弹幕显示/隐藏
        return if (isShow) "弹幕开" else "弹幕关"
    }
    
    private fun formatTime(timeMs: Long): String {
        if (timeMs <= 0) return "片头"
        return _mPlayers?.stringToTime(timeMs) ?: "00:00"
    }

    // 播放器资源释放 - 添加VOD_FLOW日志
    fun release() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RELEASE] 开始释放播放器资源")

        try {
            _mPlayers?.release()
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RELEASE_SUCCESS] 播放器资源释放成功")
            VodFlowTracker.logFlowStep(currentFlowId, "PLAYER_RELEASE", "播放器资源释放完成")
        } catch (e: Exception) {
            Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RELEASE_ERROR] 播放器资源释放异常", e)
        } finally {
            _mPlayers = null
            mHistory = null
            isInitialized = false
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [PLAYER_RELEASE_COMPLETE] 播放器状态重置完成")
        }
    }

    // 优化的播放器初始化检查
    fun ensureInitialized(): Boolean {
        return isInitialized && _mPlayers != null
    }

    // 安全的播放器操作包装
    private inline fun <T> safePlayerOperation(defaultValue: T, operation: (Players) -> T): T {
        return if (ensureInitialized()) {
            try {
                _mPlayers?.let { operation(it) } ?: defaultValue
            } catch (e: Exception) {
                defaultValue
            }
        } else {
            defaultValue
        }
    }

    /**
     * 🔥 处理播放中断 - 当播放器意外进入IDLE状态时调用
     */
    fun handlePlaybackInterruption() {
        val currentFlowId = VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
        Log.w("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_DETECTED] 检测到播放中断，尝试恢复播放")

        // 延迟3秒后尝试恢复播放，给网络缓冲一些时间
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                val players = _mPlayers
                if (players != null) {
                    // 🔥 修复：使用ExoPlayer的play()方法恢复播放，而不是start()
                    val exoPlayer = players.get()
                    if (exoPlayer != null) {
                        exoPlayer.play()
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_RECOVERY] 播放恢复成功")
                    } else {
                        Log.w("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_RECOVERY_FAILED] ExoPlayer为空，无法恢复播放")
                    }
                } else {
                    Log.w("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_RECOVERY_FAILED] Players为空，无法恢复播放")
                }
            } catch (e: Exception) {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [INTERRUPTION_RECOVERY_ERROR] 播放恢复失败: ${e.message}")
            }
        }, 3000) // 延迟3秒
    }
}

@Composable
fun rememberVideoController(activity: Activity): VideoController {
    return remember(activity) { VideoController(activity) }
}
