plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.kotlin.serialization)
    id("org.jetbrains.kotlin.kapt")
    // Python插件恢复，支持Python爬虫功能 - 版本在根项目中定义
    id("com.chaquo.python")
    // KotlinPoet专业重构 - 移除Hilt插件
    // alias(libs.plugins.hilt)
}

// Chaquo Python配置 - 按照官方文档使用顶层chaquopy块
chaquopy {
    defaultConfig {
        version = "3.8"
        pip {
            install("lxml")
            install("ujson")
            install("pyquery")
            install("requests")
            install("jsonpath")
            install("cachetools")
            install("pycryptodome")
            install("beautifulsoup4")
        }
    }
    sourceSets {
        getByName("main") {
            srcDir("src/main/python")
        }
    }
}

// 移除 KSP 配置
// ksp {
//     arg("room.schemaLocation", "$projectDir/schemas")
//     arg("room.incremental", "true")
//     arg("room.expandProjection", "true")
// }

android {
    namespace = "top.cywin.onetv.movie"
    compileSdk = libs.versions.compileSdk.get().toInt()

    // 配置电视模式构建变体，与TV主应用保持一致
    flavorDimensions += listOf("mode", "abi")

    productFlavors {
        create("leanback") {
            dimension = "mode"
        }
        create("arm64_v8a") {
            dimension = "abi"
            ndk {
                abiFilters += listOf("arm64-v8a")
            }
        }
    }

    // 只构建leanback + arm64_v8a组合，与TV模块保持一致
    variantFilter {
        val mode = flavors.find { it.dimension == "mode" }?.name
        val abi = flavors.find { it.dimension == "abi" }?.name
        if (!(mode == "leanback" && abi == "arm64_v8a")) {
            ignore = true
        }
    }

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")

        // NDK ABI过滤器配置 - Chaquo Python插件要求
        ndk {
            abiFilters += listOf("arm64-v8a")
        }

        // Python配置已移到顶层chaquopy块

        // 注解处理器配置 - 按照原项目FongMi_TV配置
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += mapOf(
                    "room.schemaLocation" to "$projectDir/schemas",
                    "eventBusIndex" to "top.cywin.onetv.movie.event.EventIndex"
                )
            }
        }

        // Python配置移到顶层

        // BuildConfig字段配置
        buildConfigField("String", "APPLICATION_ID", "\"top.cywin.onetv.tv\"")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
        // 修复Java编译器版本警告 - 基于FongMi_TV完整功能
        isCoreLibraryDesugaringEnabled = true
    }

    // 启用BuildConfig生成
    buildFeatures {
        buildConfig = true
    }

    kotlinOptions {
        jvmTarget = "1.8"
        freeCompilerArgs += listOf(
            "-Xjvm-default=all",
            "-opt-in=kotlin.RequiresOptIn",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api"
        )
    }

    // 自定义资源目录配置 - 使用主要资源目录
    sourceSets {
        getByName("main") {
            // 明确指定Java源码目录
            java.srcDirs("src/main/java")
            // 使用src/main/res作为主要资源目录
            res.srcDirs("src/main/res")
            // Python源码目录配置已在chaquopy块中设置
        }
    }

    buildFeatures {
        compose = true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = libs.versions.composeCompiler.get()
    }

    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
            // 解决Kotlin jar包冲突 - 使用新的语法
            pickFirsts.add("**/kotlin-compiler-embeddable*.jar")
            pickFirsts.add("**/kotlin-stdlib*.jar")
            pickFirsts.add("**/kotlin-reflect*.jar")
            pickFirsts.add("**/kotlin-scripting*.jar")
            // 处理META-INF冲突
            pickFirsts.add("META-INF/versions/9/previous-compilation-data.bin")
            pickFirsts.add("META-INF/com.onetv.tools/r8-from-*.version")
        }
    }

    lint {
        // 使用基线文件忽略现有的Lint错误
        baseline = file("lint-baseline.xml")
        // 不因Lint错误中断构建
        abortOnError = false
        // 跳过Release构建时的Lint检查以加快构建速度
        checkReleaseBuilds = false
        // 忽略常见的library模块Lint警告
        disable.add("MissingPermission")
        disable.add("InvalidPackage")
        disable.add("OldTargetApi")
        disable.add("UnusedResources")
    }
}

// 自定义资源处理任务 - 将vod_前缀目录转换为标准Android资源结构
// 注释掉vod相关任务，因为vod模块已删除
/*
tasks.register("processVodResources") {
    group = "movie"
    description = "处理vod_前缀的自定义资源目录结构"

    val sourceDir = file("src/main/vod_res")
    val targetDir = file("src/main/vod_res_processed")

    inputs.dir(sourceDir)
    outputs.dir(targetDir)

    doLast {
        // 清理目标目录
        if (targetDir.exists()) {
            targetDir.deleteRecursively()
        }
        targetDir.mkdirs()

        // 处理vod_values -> values
        val vodValuesDir = file("$sourceDir/vod_values")
        if (vodValuesDir.exists()) {
            val valuesDir = file("$targetDir/values")
            valuesDir.mkdirs()
            vodValuesDir.listFiles()?.forEach { file ->
                file.copyTo(File(valuesDir, file.name), overwrite = true)
            }
        }

        // 处理vod_drawable -> drawable
        val vodDrawableDir = file("$sourceDir/vod_drawable")
        if (vodDrawableDir.exists()) {
            val drawableDir = file("$targetDir/drawable")
            drawableDir.mkdirs()
            vodDrawableDir.listFiles()?.forEach { file ->
                file.copyTo(File(drawableDir, file.name), overwrite = true)
            }
        }

        println("✓ VOD资源处理完成: $sourceDir -> $targetDir")
    }
}

// 确保在资源合并前处理VOD资源
tasks.named("preBuild") {
    dependsOn("processVodResources")
}
*/

dependencies {
    // 核心模块依赖
    implementation(project(":core:data"))
    implementation(project(":core:designsystem"))
    implementation(project(":core:util"))

    // Android核心库
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.activity.compose)

    // FongMi_TV架构依赖 - ViewModel支持
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")

    // FongMi_TV架构依赖 - Fragment支持
    implementation("androidx.fragment:fragment-ktx:1.6.2")
    implementation(libs.androidx.appcompat)

    // Compose
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.compose.ui)
    implementation(libs.androidx.compose.ui.graphics)
    implementation(libs.androidx.compose.ui.tooling.preview)
    implementation(libs.androidx.compose.material3)
    implementation(libs.androidx.compose.material.icons.extended)
    implementation(libs.androidx.compose.foundation)

    // Navigation
    implementation(libs.androidx.navigation.compose)

    // 网络请求
    implementation(libs.okhttp)
    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.kotlinx.serialization)
    implementation(libs.kotlinx.serialization.json)

    // FongMi_TV架构依赖 - 网络和解析
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
    implementation("com.google.code.gson:gson:2.10.1")
    implementation("org.jsoup:jsoup:1.17.2")

    // FongMi_TV架构依赖 - 事件总线
    implementation("org.greenrobot:eventbus:3.3.1")

    // FongMi_TV架构依赖 - 日志系统
    implementation("com.orhanobut:logger:2.2.0")

    // 图片加载
    implementation(libs.coil.compose)

    // 播放器 (复用现有)
    implementation(libs.androidx.media3.exoplayer)
    implementation(libs.androidx.media3.ui)
    implementation(libs.androidx.media3.hls)
    // implementation(libs.androidx.media3.decoder.ffmpeg) // 暂时注释，避免仓库问题

    // FongMi_TV架构依赖 - 媒体和播放器扩展 (版本同步到1.6.1)
    implementation("androidx.media3:media3-session:1.6.1")
    implementation("androidx.media3:media3-common:1.6.1")
    implementation("androidx.media3:media3-datasource:1.6.1")

    // FongMi_TV架构依赖 - 通知和服务
    implementation("androidx.work:work-runtime-ktx:2.9.0")
    implementation("androidx.localbroadcastmanager:localbroadcastmanager:1.1.0")

    // 数据库 - Room依赖已在下方统一配置

    // KotlinPoet专业代码生成 - 替代Hilt依赖注入 (仅编译时使用)
    compileOnly(libs.kotlinpoet)
    compileOnly(libs.kotlinpoet.ksp)
    // 移除kotlin-compiler依赖，避免与KSP插件冲突
    // implementation(libs.kotlin.compiler)
    // implementation(libs.kotlin.scripting)

    // 协程
    implementation(libs.kotlinx.coroutines.android)

    // JSON处理
    implementation(libs.kotlinx.serialization.json)

    // Debug工具
    debugImplementation(libs.androidx.compose.ui.tooling)

    // Cling DLNA/UPnP 依赖已在下方统一配置

    // QuickJS依赖 - 按照原项目FongMi_TV配置
    implementation("wang.harlon.quickjs:wrapper-java:3.2.0")
    implementation("wang.harlon.quickjs:wrapper-android:3.2.0")
    implementation("net.sourceforge.streamsupport:android-retrofuture:1.7.4")

    // FongMi_TV核心依赖 - 媒体播放器扩展 (版本同步到1.6.1)
    implementation("androidx.media3:media3-container:1.6.1")
    implementation("androidx.media3:media3-database:1.6.1")
    implementation("androidx.media3:media3-datasource-okhttp:1.6.1")
    implementation("androidx.media3:media3-datasource-rtmp:1.6.1")
    implementation("androidx.media3:media3-decoder:1.6.1")
    implementation("androidx.media3:media3-effect:1.6.1")
    implementation("androidx.media3:media3-exoplayer-dash:1.6.1")
    implementation("androidx.media3:media3-exoplayer-rtsp:1.6.1")
    implementation("androidx.media3:media3-exoplayer-smoothstreaming:1.6.1")
    implementation("androidx.media3:media3-extractor:1.6.1")

    // FongMi_TV核心依赖 - 网络协议支持
    implementation("com.github.thegrizzlylabs:sardine-android:0.9")
    implementation("com.github.teamnewpipe:NewPipeExtractor:v0.24.6")
    implementation("com.hierynomus:smbj:0.14.0")
    implementation("io.antmedia:rtmp-client:3.2.0")

    // FongMi_TV核心依赖 - 服务器和网络服务
    implementation("org.nanohttpd:nanohttpd:2.3.1")
    implementation("org.eclipse.jetty:jetty-client:8.1.21.v20160908")
    implementation("org.eclipse.jetty:jetty-server:8.1.21.v20160908") {
        exclude(group = "org.eclipse.jetty.orbit", module = "javax.servlet")
    }
    implementation("org.eclipse.jetty:jetty-servlet:8.1.21.v20160908") {
        exclude(group = "org.eclipse.jetty.orbit", module = "javax.servlet")
    }
    implementation("javax.servlet:javax.servlet-api:3.1.0")

    // FongMi_TV核心依赖 - 图像处理
    implementation("com.github.bumptech.glide:glide:4.16.0")
    implementation("com.github.bumptech.glide:annotations:4.16.0")
    implementation("com.github.bumptech.glide:avif-integration:4.16.0") {
        exclude(group = "org.aomedia.avif.android", module = "avif")
    }
    implementation("com.github.bumptech.glide:okhttp3-integration:4.16.0")
    implementation("org.aomedia.avif.android:avif:1.1.1.14d8e3c4")

    // FongMi_TV核心依赖 - 工具库
    implementation("com.google.zxing:core:3.5.3")
    implementation("com.guolindev.permissionx:permissionx:1.8.0")
    implementation("org.simpleframework:simple-xml:2.7.1") {
        exclude(group = "stax", module = "stax-api")
        exclude(group = "xpp3", module = "xpp3")
    }
    implementation("cat.ereza:customactivityoncrash:2.4.0")

    // FongMi_TV核心依赖 - 本地AAR文件已移动到TV主应用模块
    // implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar")))) // 已移动到tv/libs

    // FongMi_TV核心依赖 - 弹幕库NDK支持 (原项目未使用，暂时注释)
    // implementation("com.github.ctiao:ndkbitmap-armv7a:0.9.21")
    // implementation("com.github.ctiao:ndkbitmap-armv5:0.9.21")
    // implementation("com.github.ctiao:ndkbitmap-x86:0.9.21")

    // FongMi_TV核心依赖 - 缺少的重要依赖
    implementation("androidx.media:media:1.7.0")
    implementation("com.github.anilbeesetti.nextlib:nextlib-media3ext:0.8.4") {
        exclude(group = "androidx.media3")
    }
    implementation("com.github.bassaer:materialdesigncolors:1.0.0")
    implementation("com.github.jahirfiquitiva:TextDrawable:1.0.3")
    implementation("com.google.android.material:material:1.12.0")

    // FongMi_TV核心依赖 - Cling版本同步
    implementation("org.fourthline.cling:cling-core:2.1.1")
    implementation("org.fourthline.cling:cling-support:2.1.1")

    // FongMi_TV核心依赖 - Room数据库
    implementation("androidx.room:room-runtime:2.7.1")
    kapt("androidx.room:room-compiler:2.7.1")

    // FongMi_TV核心依赖 - 通用依赖 (移除构建变体特定依赖)
    implementation("androidx.leanback:leanback:1.2.0")
    implementation("com.github.JessYanCoding:AndroidAutoSize:1.2.1")
    implementation("androidx.biometric:biometric:1.1.0")
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")
    implementation("com.google.android.flexbox:flexbox:3.0.0")
    implementation("com.journeyapps:zxing-android-embedded:4.3.0") {
        isTransitive = false
    }

    // FongMi_TV核心依赖 - Python支持 (Chaquo Python插件自动处理)
    // implementation("com.chaquo.python:target:15.0.1") // 由插件自动添加

    // FongMi_TV核心依赖 - DNS over HTTPS支持
    implementation("com.squareup.okhttp3:okhttp-dnsoverhttps:4.12.0")

    // FongMi_TV核心依赖 - Preference支持
    implementation("androidx.preference:preference-ktx:1.2.1")

    // FongMi_TV架构：Library模块使用compileOnly访问AAR类，运行时由TV模块提供
    // 使用compileOnly配置让movie模块在编译时能访问AAR中的类，避免打包冲突
    compileOnly(files("../tv/libs/dfm-release.aar"))
    compileOnly(files("../tv/libs/forcetech-release.aar"))
    compileOnly(files("../tv/libs/hook-release.aar"))
    compileOnly(files("../tv/libs/jianpian-release.aar"))
    compileOnly(files("../tv/libs/thunder-release.aar"))
    compileOnly(files("../tv/libs/tvbus-release.aar"))
    compileOnly(files("../tv/libs/dlna-core-release.aar"))
    compileOnly(files("../tv/libs/dlna-dmc-release.aar"))
    compileOnly(files("../tv/libs/dlna-dmr-release.aar"))

    // AAR文件由TV模块管理和提供，movie模块只在编译时访问类定义

    // FongMi_TV核心依赖 - 注解处理器
    kapt("com.github.bumptech.glide:compiler:4.16.0")
    kapt("org.greenrobot:eventbus-annotation-processor:3.3.1")

    // FongMi_TV核心依赖 - 核心库脱糖 (使用统一版本配置)
    coreLibraryDesugaring(libs.desugar.jdk)
}

// 修复kapt编译顺序问题 - 基于FongMi_TV完整功能
kapt {
    correctErrorTypes = true
    useBuildCache = true
    includeCompileClasspath = true  // 包含编译类路径，确保kapt能找到所有类

    // 修复kapt语言版本警告 - 明确指定使用Kotlin 1.9
    arguments {
        arg("room.schemaLocation", "$projectDir/schemas")
        arg("eventBusIndex", "top.cywin.onetv.movie.event.EventIndex")
        arg("kapt.kotlin.generated", "$buildDir/generated/source/kapt/main")
    }

    javacOptions {
        // 确保Java编译选项正确
        option("-Xlint:unchecked")
        option("-Xlint:deprecation")
    }
}
