# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Log/OS Files
*.log

# Android Studio generated files and folders
captures/
.externalNativeBuild/
.cxx/
*.apk
output.json

# IntelliJ
*.iml
.idea/
misc.xml
deploymentTargetDropDown.xml
render.experimental.xml

# Keystore files
*.jks
*.keystore

# Google Services (e.g. APIs or Firebase)
google-services.json

# Android Profiling
*.hprof

# Sensitive information
supabase_config.properties
node_modules/
package.json
package-lock.json

# Environment variables and secrets
.env
.env.*
*.env
env.local
env.development.local
env.test.local
env.production.local

# API keys and secrets
api_keys.txt
secrets.yaml
secrets.yml
secrets.properties

# Authentication files
auth_config.json
auth.json
credentials.json

# Configuration files that might contain sensitive data
config.json
settings.json
application.properties
application.yml
application.yaml
/local.properties.example

# Additional directories and files to ignore
/CSV/
/MD/
/Review-Gate/
/qrose-local/
Cursor-Rules.md
USER_RULES.md
/googel/
/supabase-kt-3.1.4/
/Review-Gate/

# Supabase特定排除（保留功能允许自动部署）
/supabase/.temp/
/supabase/.env
# 不要排除整个supabase目录，只排除敏感或临时文件

# 敏感信息和配置文件
# 明确列出要排除的properties文件，而不是使用通配符
local.properties
keystore.properties
key.properties
supabase_config.properties
secrets.properties
application.properties
# 保留重要配置文件
# gradle.properties和gradle-wrapper.properties不在排除列表中
!supabase/config.toml  # 保留Supabase配置文件
*.keystore
*.jks

# 环境变量文件
.env
.env.*

# 构建文件和目录
/build/
/release
/sentry.properties
/OneTV_Supabase_Key
/app/build/
/*/build/
.gradle/
.idea/
*.iml
.DS_Store

# 日志文件
*.log
build.log
build_tail.log
build_head.log
onetv_error.log

# 临时文件
*.tmp
*.temp
/tmp/
/temp/

# 编译输出
/bin/
/out/
/gen/
/captures/

# 特定于IDE的文件
.externalNativeBuild/
.cxx/
*.swp
*.swo
.cursor/
.vscode/

# 特定于Android的文件
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
/proguardFiles/
/lint/intermediates/
/lint/generated/
/lint/outputs/
/lint/tmp/

# 用户特定设置
.gradle/
.idea/
/py/
RELEASE_GUIDE.md
GIT_GUIDE.md

# 激活码文件
activation_codes_*.txt

# 备份目录
/backup/

# 自动生成的文件
.kotlin/

# 测试JSON文件
tv-beta.json

# 其他项目特定文件
exclude.txt
onetvkey.jks
iptv-V1-supabase.js

# 运行时生成的文件
/screenshots/
/email_accounts.csv
/gradle-8.7-bin.zip

# 新增的Supabase文档文件
SupabaseCacheCompatibilityGuide.md
SupabaseCacheImplementationGuide.md
SupabaseCacheImplementationStatus_v1.md
SupabaseCacheOptimizationCompletionReport.md
SupabaseCacheOptimizationPlan.md
SupabaseWatchHistorySyncServiceGuide.md
GIT_GUIDE.md
/supabase/sql/
/MD/test_vip_permission_fix.md

# 参考项目 - 不进行版本控制

/FongMi_TV/
# /filmMD/ - 已删除
# /film/ - 已删除
# /onevodMD/ - 已删除
# /vodMD/ - 已删除
/scripts/
/movie_backup_20250717_155153/
# 点播功能设计文档 - 不进行版本控制
/movieMD/
/movieMD/
supabase/debug/check_service_key.sql
# supabase/deploy_vod_config.sh - 已删除
supabase/migrations/20250711_rename_supabase_service_to_service_role.sql
TVBOX_ROUTE_LOGIC_FIX.md
# onevod/README.md - 已删除
# onevod/LICENSE.md - 已删除

# /film/ - 已删除
# vodMD/00_日志.txt - 已删除
# vodMD/00_日志.txt - 已删除
# vodMD/00_日志.txt - 已删除
# vodMD/00_日志.txt - 已删除
MOVIE_NAVIGATION_CHANGE_REPORT.md
/movie_stage2_backup_20250717_211459/
vodMD/19_项目构建问题分析与修复方案_20250724.md
F o n g M i _ T V / 
 
 
movie/src/main/assets/jar/22.jar
movie/src/main/assets/jar/spider备份.jar
fix_imports.ps1
fix_imports_simple.ps1
THIRD_PARTY_MODULES.md
PACKAGE_MAPPING_QUICK_REF.md
.augment/rules/AURA.md
AURA.md
《原版播放器功能全部移植集成施工方案》.md
《原版FongMi_TV播放器功能全部移植集成施工方案V2》.md
