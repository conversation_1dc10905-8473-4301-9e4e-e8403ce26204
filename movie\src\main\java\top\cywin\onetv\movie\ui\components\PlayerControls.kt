package top.cywin.onetv.movie.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import top.cywin.onetv.movie.player.VideoController
import top.cywin.onetv.movie.ui.model.Episode

/**
 * 播放器控制栏 - 直接移植原版控制栏布局
 * 基于：FongMi_TV/src/leanback/res/layout/vod_view_control_vod.xml
 */
@Composable
fun PlayerControls(
    controller: VideoController,
    episodes: List<Episode>,
    currentEpisodeIndex: Int,
    isVisible: Boolean,
    onEpisodeSelect: (Int) -> Unit,
    onRefresh: () -> Unit,
    onChangeSource: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 添加原版显示/隐藏动画
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { it },
            animationSpec = tween(300)
        ) + fadeIn(animationSpec = tween(300)),
        exit = slideOutVertically(
            targetOffsetY = { it },
            animationSpec = tween(300)
        ) + fadeOut(animationSpec = tween(300))
    ) {
        // 完全复制原版背景样式
        Column(
            modifier = modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color.Black.copy(alpha = 0.8f)
                        )
                    ),
                    shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
                )
                .padding(start = 16.dp, top = 18.dp, end = 16.dp, bottom = 8.dp)
        ) {
            // 移植原版HorizontalScrollView + LinearLayout结构
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .horizontalScroll(rememberScrollState()),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 直接移植原版14个控制按钮
                ControlButtonRow(
                    controller = controller,
                    episodes = episodes,
                    currentEpisodeIndex = currentEpisodeIndex,
                    onEpisodeSelect = onEpisodeSelect,
                    onRefresh = onRefresh,
                    onChangeSource = onChangeSource
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 直接移植原版进度条：android:id="@+id/seek" (CustomSeekView)
            PlayerSeekBar(controller = controller)
        }
    }
}

@Composable
private fun ControlButtonRow(
    controller: VideoController,
    episodes: List<Episode>,
    currentEpisodeIndex: Int,
    onEpisodeSelect: (Int) -> Unit,
    onRefresh: () -> Unit,
    onChangeSource: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 所有14个原版控制按钮的状态
    var decodeText by remember { mutableStateOf("硬解") }
    var speedText by remember { mutableStateOf("1.00") }
    var scaleText by remember { mutableStateOf("原始") }
    var textTrackText by remember { mutableStateOf("字幕") }
    var audioTrackText by remember { mutableStateOf("音轨") }
    var videoTrackText by remember { mutableStateOf("视轨") }
    var openingText by remember { mutableStateOf("片头") }
    var endingText by remember { mutableStateOf("片尾") }
    var loopText by remember { mutableStateOf("循环") }
    var danmakuText by remember { mutableStateOf("弹幕") }
    var resetText by remember { mutableStateOf("刷新") }
    var changeSourceText by remember { mutableStateOf("换源") }
    var playerText by remember { mutableStateOf("EXO") }

    Row(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 按原版顺序排列所有14个按钮
        // 下集按钮
        ControlButton(
            text = "下集",
            onClick = {
                if (currentEpisodeIndex < episodes.size - 1) {
                    onEpisodeSelect(currentEpisodeIndex + 1)
                }
            }
        )

        // 上集按钮
        ControlButton(
            text = "上集",
            onClick = {
                if (currentEpisodeIndex > 0) {
                    onEpisodeSelect(currentEpisodeIndex - 1)
                }
            }
        )

        // 刷新按钮
        ControlButton(
            text = resetText,
            onClick = {
                resetText = controller.onResetToggle()
                onRefresh()
            }
        )

        // 换源按钮
        ControlButton(
            text = changeSourceText,
            onClick = {
                changeSourceText = controller.onChangeSource()
                onChangeSource()
            }
        )

        // 播放器切换按钮
        ControlButton(
            text = playerText,
            onClick = { playerText = controller.onPlayer() }
        )

        // 解码模式按钮
        ControlButton(
            text = decodeText,
            onClick = { decodeText = controller.onDecode() }
        )

        // 播放速度按钮
        ControlButton(
            text = speedText,
            onClick = { speedText = controller.onSpeed() }
        )

        // 缩放模式按钮
        ControlButton(
            text = scaleText,
            onClick = { scaleText = controller.onScale() }
        )

        // 字幕轨按钮
        ControlButton(
            text = textTrackText,
            onClick = { textTrackText = controller.onTextTrack() }
        )

        // 音轨按钮
        ControlButton(
            text = audioTrackText,
            onClick = { audioTrackText = controller.onAudioTrack() }
        )

        // 视频轨按钮
        ControlButton(
            text = videoTrackText,
            onClick = { videoTrackText = controller.onVideoTrack() }
        )

        // 片头按钮
        ControlButton(
            text = openingText,
            onClick = { openingText = controller.onOpening() }
        )

        // 片尾按钮
        ControlButton(
            text = endingText,
            onClick = { endingText = controller.onEnding() }
        )

        // 循环播放按钮
        ControlButton(
            text = loopText,
            onClick = { loopText = controller.onLoop() }
        )

        // 弹幕按钮
        ControlButton(
            text = danmakuText,
            onClick = { danmakuText = controller.onDanmaku() }
        )
    }
}

// 直接移植原版TextView样式
@Composable
private fun ControlButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        modifier = modifier
            .clip(RoundedCornerShape(4.dp))
            .background(Color.Transparent)
            .clickable { onClick() }
            .padding(horizontal = 8.dp, vertical = 4.dp),
        color = Color.White,
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal
    )
}

// 直接移植原版CustomSeekView
@Composable
private fun PlayerSeekBar(
    controller: VideoController,
    modifier: Modifier = Modifier
) {
    // 播放器状态
    var currentTime by remember { mutableStateOf("00:00") }
    var totalTime by remember { mutableStateOf("00:00") }
    var progress by remember { mutableStateOf(0f) }

    // 优化的播放进度更新 - 减少不必要的计算
    LaunchedEffect(controller) {
        while (true) {
            if (controller.isInitialized) {
                val players = controller.mPlayers
                if (players != null) {
                    try {
                        val position = players.position
                        val duration = players.duration

                        // 只在有效时间时更新UI
                        if (position >= 0 && duration > 0) {
                            currentTime = players.stringToTime(position)
                            totalTime = players.durationTime
                            progress = position.toFloat() / duration.toFloat()
                        }
                    } catch (e: Exception) {
                        // 静默处理异常，避免崩溃
                    }
                }
            }
            kotlinx.coroutines.delay(1000) // 保持1秒更新频率
        }
    }

    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 当前时间
        Text(
            text = currentTime,
            color = Color.White,
            fontSize = 12.sp
        )

        // 进度条
        Box(
            modifier = Modifier
                .weight(1f)
                .height(4.dp)
                .background(Color.Gray.copy(alpha = 0.3f), RoundedCornerShape(2.dp))
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(progress.coerceIn(0f, 1f))
                    .background(Color.White, RoundedCornerShape(2.dp))
            )
        }

        // 总时间
        Text(
            text = totalTime,
            color = Color.White,
            fontSize = 12.sp
        )
    }
}