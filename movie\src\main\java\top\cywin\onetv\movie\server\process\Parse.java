package top.cywin.onetv.movie.server.process;

import top.cywin.onetv.movie.server.Nano;
import top.cywin.onetv.movie.server.impl.Process;
import top.cywin.onetv.movie.utils.VodFlowTracker;
import com.github.catvod.utils.Asset;

import java.util.Map;

import fi.iki.elonen.NanoHTTPD;

public class Parse implements Process {

    @Override
    public boolean isRequest(NanoHTTPD.IHTTPSession session, String url) {
        return url.startsWith("/parse");
    }

    @Override
    public NanoHTTPD.Response doResponse(NanoHTTPD.IHTTPSession session, String url, Map<String, String> files) {
        // 🔥 关键修复：获取当前FlowID用于解析请求日志
        String currentFlowId = VodFlowTracker.INSTANCE.getCurrentFlowId();
        String flowIdPrefix = currentFlowId != null ? "[FlowID:" + currentFlowId + "] " : "";

        try {
            Map<String, String> params = session.getParms();
            String jxs = params.get("jxs");
            String parseUrl = params.get("url");

            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[PARSE_REQUEST] 解析请求: jxs=" + jxs + ", url=" + parseUrl);

            String html = String.format(Asset.read("parse.html"), jxs, parseUrl);

            android.util.Log.d("VOD_FLOW", flowIdPrefix + "[PARSE_RESPONSE] 解析页面生成成功");

            return NanoHTTPD.newFixedLengthResponse(NanoHTTPD.Response.Status.OK, NanoHTTPD.MIME_HTML, html);
        } catch (Exception e) {
            android.util.Log.e("VOD_FLOW", flowIdPrefix + "[PARSE_ERROR] 解析请求失败: " + e.getMessage(), e);
            return Nano.error(e.getMessage());
        }
    }
}
