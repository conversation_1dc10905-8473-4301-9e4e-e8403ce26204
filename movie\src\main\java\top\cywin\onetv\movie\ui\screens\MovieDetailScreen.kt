package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.Fullscreen
import androidx.compose.material.icons.filled.Error
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import kotlinx.coroutines.delay
import androidx.compose.material3.*
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import top.cywin.onetv.movie.viewmodel.MovieDetailViewModel
import top.cywin.onetv.movie.viewmodel.DetailUiState
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.bean.Flag
import top.cywin.onetv.movie.ui.model.PlayFlag
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.ui.model.Episode
import top.cywin.onetv.movie.MovieApp
import android.util.Log
import coil.compose.AsyncImage

/**
 * OneTV Movie详情页面 - 按照FongMi_TV整合指南重构
 */
@Composable
fun MovieDetailScreen(
    vodId: String,
    siteKey: String = "",
    navController: NavController,
    viewModel: MovieDetailViewModel = viewModel {
        MovieDetailViewModel()
    }
) {
    // 🔥 关键修复：获取全局FlowID
    val globalFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getGlobalFlowId()
    Log.d("ONETV_DETAIL_SCREEN", "🎬 [电影ID跟踪] 详情页面启动: vodId=$vodId, siteKey=$siteKey, globalFlowId=$globalFlowId")
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(vodId, siteKey) {
        Log.d("ONETV_DETAIL_SCREEN", "🎬 [FongMi_TV兼容] 开始加载电影详情: vodId=$vodId, siteKey=$siteKey, globalFlowId=$globalFlowId")
        Log.d("ONETV_DETAIL_SCREEN", "🎯 [电影ID跟踪] 详情页面初始化，调用loadMovieDetail")
        // 🔥 关键修复：传递全局FlowID到ViewModel
        viewModel.loadMovieDetail(vodId, siteKey, globalFlowId)
    }

    // 🔥 修复：监听自动播放状态，在详情页面内嵌播放器中播放，不跳转到全屏
    LaunchedEffect(uiState.shouldAutoPlay) {
        if (uiState.shouldAutoPlay) {
            val currentEpisode = uiState.currentEpisode
            if (currentEpisode != null && !currentEpisode.playUrl.isNullOrEmpty()) {
                Log.d("ONETV_DETAIL_SCREEN", "🎬 [自动播放] 播放地址解析完成，在内嵌播放器中自动播放: ${currentEpisode.playUrl}")

                // 🔥 确保FlowID连续性
                val currentFlowId = globalFlowId ?: top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
                top.cywin.onetv.movie.utils.VodFlowTracker.setGlobalFlowId(currentFlowId)

                // 🔥 修复：不跳转到全屏播放器，而是在内嵌播放器中自动播放
                android.util.Log.d("ONETV_DETAIL_SCREEN", "🎬 [自动播放] 在详情页面内嵌播放器中自动播放，FlowID: $currentFlowId")

                // 重置自动播放标志
                viewModel.resetAutoPlayFlag()
            }
        }
    }

    // 🔥 监听内嵌播放器自动播放完成
    LaunchedEffect(uiState.shouldEmbeddedAutoPlay) {
        if (uiState.shouldEmbeddedAutoPlay) {
            // 延迟一段时间后重置标志，模拟自动播放完成
            kotlinx.coroutines.delay(1000)
            viewModel.resetEmbeddedAutoPlayFlag()
        }
    }

    // ✅ UI内容渲染
    DetailContent(
        uiState = uiState,
        onBack = { navController.popBackStack() },
        onPlay = { episode, episodeIndex ->
            // 🔥 关键修复：确保播放器页面能够获取到FlowID
            val currentFlowId = globalFlowId ?: top.cywin.onetv.movie.utils.VodFlowTracker.generateFlowId()
            top.cywin.onetv.movie.utils.VodFlowTracker.setGlobalFlowId(currentFlowId)
            android.util.Log.d("ONETV_DETAIL_SCREEN", "🎬 [播放器导航] 用户点击播放，FlowID: $currentFlowId, episode: ${episode.name}")
            navController.navigate(top.cywin.onetv.movie.navigation.MovieRoutes.player(vodId, episodeIndex, siteKey))
        },
        onToggleFavorite = { viewModel.toggleFavorite() },
        onFlagSelect = { flag -> viewModel.selectFlag(flag) },
        onEpisodeSelect = { episode -> viewModel.selectEpisode(episode) },
        onShowFlagSelector = { viewModel.showFlagSelector() },
        onHideFlagSelector = { viewModel.hideFlagSelector() },
        onShowEpisodeSelector = { viewModel.showEpisodeSelector() },
        onHideEpisodeSelector = { viewModel.hideEpisodeSelector() }
    )
}

@Composable
private fun DetailContent(
    uiState: DetailUiState,
    onBack: () -> Unit,
    onPlay: (Episode, Int) -> Unit,
    onToggleFavorite: () -> Unit,
    onFlagSelect: (PlayFlag) -> Unit,
    onEpisodeSelect: (Episode) -> Unit,
    onShowFlagSelector: () -> Unit,
    onHideFlagSelector: () -> Unit,
    onShowEpisodeSelector: () -> Unit,
    onHideEpisodeSelector: () -> Unit
) {
    // 🎬 统一的电影详情页面布局架构 - 所有状态都使用相同布局
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 🎬 主要内容区域 - 固定高度，不滚动
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // A区域（上左）+ B区域（上右）：电影信息 + 播放器
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.5f), // 占用50%的高度
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // A区域：电影详细信息（左侧）- 始终显示
                when {
                    uiState.isLoading -> {
                        // 加载状态下显示占位信息
                        LoadingMovieInfoSection(
                            modifier = Modifier.weight(0.55f)
                        )
                    }
                    uiState.error != null -> {
                        // 错误状态下显示基本信息
                        ErrorMovieInfoSection(
                            movieName = uiState.movie?.vodName ?: "加载失败",
                            modifier = Modifier.weight(0.55f)
                        )
                    }
                    else -> {
                        // 正常状态下显示完整信息
                        uiState.movie?.let { movie ->
                            MovieInfoSection(
                                movie = movie,
                                modifier = Modifier.weight(0.55f),
                                onToggleFavorite = onToggleFavorite,
                                isFavorite = uiState.isFavorite
                            )
                        }
                    }
                }

                // B区域：播放器区域（右侧）- 根据状态显示不同内容
                when {
                    uiState.isLoading -> {
                        // 在播放器区域显示加载状态
                        LoadingPlayerSection(
                            modifier = Modifier
                                .weight(0.45f)
                                .height(200.dp)
                        )
                    }
                    uiState.error != null -> {
                        // 在播放器区域显示错误信息
                        ErrorPlayerSection(
                            errorMessage = uiState.error ?: "未知错误",
                            onRetry = { /* 重试逻辑 */ },
                            onBack = onBack,
                            modifier = Modifier
                                .weight(0.45f)
                                .height(200.dp)
                        )
                    }
                    else -> {
                        // 正常状态下显示播放器
                        if (uiState.episodes.isNotEmpty() && uiState.currentEpisode != null) {
                            EmbeddedPlayerSection(
                                episode = uiState.currentEpisode!!,
                                isParsingPlayUrl = uiState.isParsingPlayUrl,
                                shouldAutoPlay = uiState.shouldEmbeddedAutoPlay,
                                modifier = Modifier
                                    .weight(0.45f)
                                    .height(200.dp),
                                onPlay = { episode ->
                                    val episodeIndex = uiState.episodes.indexOf(episode)
                                    onPlay(episode, episodeIndex)
                                }
                            )
                        }
                    }
                }
            }

            // C区域：播放线路选择（中间）- 只在正常状态下显示
            if (!uiState.isLoading && uiState.error == null && uiState.playFlags.isNotEmpty()) {
                PlayFlagSection(
                    flags = uiState.playFlags,
                    selectedFlag = uiState.currentFlag,
                    onFlagSelect = onFlagSelect,
                    modifier = Modifier.height(60.dp)
                )
            }

            // D区域：选集播放（下方）- 只在正常状态下显示
            if (!uiState.isLoading && uiState.error == null && uiState.episodes.isNotEmpty()) {
                EpisodeSection(
                    episodes = uiState.episodes,
                    selectedEpisode = uiState.currentEpisode,
                    onEpisodeSelect = onEpisodeSelect,
                    onPlay = onPlay,
                    modifier = Modifier.weight(0.35f)
                )
            }
        }
    }

}

// ✅ 按照指南添加必要的辅助Composable函数

@Composable
private fun LoadingMovieInfoSection(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.height(200.dp),
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // 电影基本信息区域 - 显示占位内容
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Text(
                text = "加载中...",
                style = MaterialTheme.typography.headlineSmall.copy(fontSize = 18.sp),
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = "正在获取详情信息...",
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // 功能按钮区域 - 占位
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp),
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            repeat(3) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(32.dp)
                        .background(
                            Color.Gray.copy(alpha = 0.3f),
                            RoundedCornerShape(16.dp)
                        )
                )
            }
        }
    }
}

@Composable
private fun ErrorMovieInfoSection(
    movieName: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.height(200.dp),
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // 电影基本信息区域 - 显示基本信息
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Text(
                text = movieName,
                style = MaterialTheme.typography.headlineSmall.copy(fontSize = 18.sp),
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = "详情加载失败，请重试",
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                color = Color.Gray,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // 功能按钮区域 - 禁用状态
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp),
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            repeat(3) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(32.dp)
                        .background(
                            Color.Gray.copy(alpha = 0.2f),
                            RoundedCornerShape(16.dp)
                        )
                )
            }
        }
    }
}

@Composable
private fun LoadingPlayerSection(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(48.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "正在加载详情...",
                    color = Color.White,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun ErrorPlayerSection(
    errorMessage: String,
    onRetry: () -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = "错误",
                    tint = Color.Red,
                    modifier = Modifier.size(48.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = errorMessage,
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Button(
                        onClick = onRetry
                    ) {
                        Text("重试")
                    }
                    Button(
                        onClick = onBack
                    ) {
                        Text("返回")
                    }
                }
            }
        }
    }
}

@Composable
private fun LoadingScreen(message: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = message)
        }
    }
}

@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(onClick = onRetry) {
                    Text("重试")
                }
                OutlinedButton(onClick = onBack) {
                    Text("返回")
                }
            }
        }
    }
}

@Composable
private fun MovieInfoSection(
    movie: MovieItem,
    modifier: Modifier = Modifier,
    onToggleFavorite: () -> Unit,
    isFavorite: Boolean
) {
    // 🎬 A区域：电影详细信息（参考架构图左侧区域）
    Column(
        modifier = modifier.height(200.dp), // 🔥 修复：固定高度与播放器对齐
        verticalArrangement = Arrangement.SpaceBetween // 🔥 修复：上下分布，功能按钮固定在底部
    ) {
        // 🎬 电影基本信息区域 - 按用户要求调整字体大小和显示内容
        Column(
            modifier = Modifier.weight(1f), // 占用剩余空间
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            // 剧名 - 比下方信息字体大一号
            Text(
                text = movie.vodName,
                style = MaterialTheme.typography.headlineSmall.copy(fontSize = 18.sp),
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // 来源 - 显示完整站点名称
            Text(
                text = "来源: ${getSiteDisplayName(movie.siteKey)}",
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                color = MaterialTheme.colorScheme.primary
            )

            // 演员 - 清理特殊字符，只显示一行
            if (movie.vodActor.isNotEmpty()) {
                Text(
                    text = "演员: ${cleanActorDirectorName(movie.vodActor)}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 导演 - 清理特殊字符
            if (movie.vodDirector.isNotEmpty()) {
                Text(
                    text = "导演: ${cleanActorDirectorName(movie.vodDirector)}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 🎬 删除播放地址行 - 按用户要求留给简介更多空间

            // 内容简介 - 限制高度，为功能按钮留出空间
            if (movie.vodContent.isNotEmpty()) {
                Text(
                    text = "简介: ${movie.vodContent}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 3, // 减少行数，为按钮留空间
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 🔥 修复：功能按钮区域固定在底部，与播放器下边框齐平
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp), // 固定按钮区域高度
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
                // 快速搜索 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 实现快速搜索 */ },
                    label = {
                        Text(
                            text = "快速搜索",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Search,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 集数正/倒序 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 实现集数排序 */ },
                    label = {
                        Text(
                            text = "倒序",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Sort,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 加入收藏 - 使用FilterChip样式
                FilterChip(
                    onClick = onToggleFavorite,
                    label = {
                        Text(
                            text = if (isFavorite) "已收藏" else "收藏",
                            fontSize = 12.sp
                        )
                    },
                    selected = isFavorite,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            if (isFavorite) Icons.Default.Favorite else Icons.Default.Add,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 内容简介 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 显示完整简介 */ },
                    label = {
                        Text(
                            text = "简介",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Info,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
            }
    }
}

// 🎬 B区域：内嵌播放器（参考架构图右侧区域）
@Composable
private fun EmbeddedPlayerSection(
    episode: Episode,
    isParsingPlayUrl: Boolean = false,
    shouldAutoPlay: Boolean = false,  // 🔥 新增：是否应该自动播放
    modifier: Modifier = Modifier,
    onPlay: ((Episode) -> Unit)? = null,
    onAutoPlayComplete: (() -> Unit)? = null  // 🔥 新增：自动播放完成回调
) {
    // 🔥 修复：使用真正的视频播放器而不是占位符
    val context = LocalContext.current
    var isPlaying by remember { mutableStateOf(false) }

    // 🔥 监听自动播放状态
    LaunchedEffect(shouldAutoPlay) {
        if (shouldAutoPlay && episode.playUrl.isNotEmpty()) {
            Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [自动播放] 开始在内嵌播放器中自动播放: ${episode.playUrl}")
            isPlaying = true
            onAutoPlayComplete?.invoke()
        }
    }

    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black),
            contentAlignment = Alignment.Center
        ) {
            when {
                isParsingPlayUrl -> {
                    // 🔥 显示播放地址解析状态
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color.White,
                            modifier = Modifier.size(48.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "正在解析播放地址",
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = episode.name,
                            color = Color.White.copy(alpha = 0.7f),
                            style = MaterialTheme.typography.bodySmall,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                episode.playUrl.isNotEmpty() -> {
                    // 🔥 播放地址解析成功，显示真正的视频播放器
                    EmbeddedVideoPlayer(
                        playUrl = episode.playUrl,
                        isPlaying = isPlaying,
                        onPlayPause = { isPlaying = !isPlaying },
                        onFullScreen = { onPlay?.invoke(episode) },
                        modifier = Modifier.fillMaxSize()
                    )
                }
                else -> {
                    // 🔥 等待播放地址解析
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            Icons.Default.PlayArrow,
                            contentDescription = "等待播放",
                            tint = Color.White.copy(alpha = 0.5f),
                            modifier = Modifier.size(64.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = episode.name,
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "等待播放地址解析",
                            color = Color.White.copy(alpha = 0.7f),
                            style = MaterialTheme.typography.bodySmall,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun InfoRow(label: String, value: String?) {
    value?.let {
        if (it.isNotEmpty()) {
            Row {
                Text(
                    text = "$label: ",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

// 🎬 C区域：播放线路选择（参考架构图中间区域）
@Composable
private fun PlayFlagSection(
    flags: List<PlayFlag>,
    selectedFlag: PlayFlag?,
    onFlagSelect: (PlayFlag) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 🎬 注释掉"在线播放"标题 - 按用户要求释放空间
        /*
        Text(
            text = "在线播放",
            style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp),
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp),
            color = MaterialTheme.colorScheme.primary
        )
        */

        // 🎬 线路标签可左右滑动
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(flags) { flag ->
                FilterChip(
                    onClick = { onFlagSelect(flag) },
                    label = {
                        Text(
                            text = flag.flag ?: "未知线路",
                            fontSize = 12.sp
                        )
                    },
                    selected = selectedFlag == flag,
                    modifier = Modifier.height(36.dp)
                )
            }
        }
    }
}

// 🎬 D区域：选集播放（参考架构图下方区域）
@Composable
private fun EpisodeSection(
    episodes: List<Episode>,
    selectedEpisode: Episode?,
    onEpisodeSelect: (Episode) -> Unit,
    onPlay: (Episode, Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 🎬 注释掉"选集播放"标题 - 按用户要求释放空间
        /*
        Text(
            text = "选集播放",
            style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp),
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp),
            color = MaterialTheme.colorScheme.primary
        )
        */

        // 🎬 剧集网格布局 - 固定每行8个标签，平均分布
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 🎬 固定每行显示8个集数标签 - 按用户要求
            val itemsPerRow = 8
            val chunkedEpisodes = episodes.chunked(itemsPerRow)

            items(chunkedEpisodes) { rowEpisodes ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    rowEpisodes.forEachIndexed { localIndex, episode ->
                        val globalIndex = episodes.indexOf(episode)
                        EpisodeChip(
                            episode = episode,
                            isSelected = selectedEpisode == episode,
                            onClick = {
                                // 🔥 关键修复：只调用onEpisodeSelect，它会自动解析播放地址并更新内嵌播放器
                                // 不直接调用onPlay，避免立即跳转到全屏播放器
                                onEpisodeSelect(episode)
                            },
                            modifier = Modifier.weight(1f) // 平均分布，铺满一行
                        )
                    }

                    // 如果这一行不足8个，用空白填充
                    repeat(itemsPerRow - rowEpisodes.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
    }
}

@Composable
private fun EpisodeChip(
    episode: Episode,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = {
            Text(
                text = episode.name,
                style = MaterialTheme.typography.bodySmall.copy(
                    fontSize = 11.sp,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal // 🔥 修复：选中时加粗
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center, // 🎬 文字左右居中
                modifier = Modifier.fillMaxWidth(), // 确保文字在整个标签宽度内居中
                color = if (isSelected) Color.White else LocalContentColor.current // 🔥 修复：选中时白色文字
            )
        },
        selected = isSelected,
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary, // 🔥 修复：选中时主色调背景
            selectedLabelColor = Color.White, // 🔥 修复：选中时白色文字
            containerColor = MaterialTheme.colorScheme.surface, // 🔥 修复：未选中时表面色
            labelColor = MaterialTheme.colorScheme.onSurface // 🔥 修复：未选中时表面文字色
        ),
        border = if (isSelected) {
            FilterChipDefaults.filterChipBorder(
                enabled = true,
                selected = true,
                borderColor = MaterialTheme.colorScheme.primary,
                selectedBorderColor = MaterialTheme.colorScheme.primary,
                borderWidth = 2.dp // 🔥 修复：选中时加粗边框
            )
        } else {
            FilterChipDefaults.filterChipBorder(
                enabled = true,
                selected = false
            )
        },
        modifier = modifier
            .height(32.dp), // 调整高度以适应新布局
        leadingIcon = if (isSelected) {
            {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp)
                )
            }
        } else null
    )
}

// 🎬 工具函数：清理演员和导演名字中的特殊字符
private fun cleanActorDirectorName(name: String): String {
    if (name.isEmpty()) return name

    // 移除方括号及其内容，例如：[a=cr:{"id":"宿宇杰/{pg}
    val withoutBrackets = name.replace(Regex("\\[.*?\\]"), "")

    // 移除花括号及其内容
    val withoutBraces = withoutBrackets.replace(Regex("\\{.*?\\}"), "")

    // 移除其他特殊字符，保留中文、英文、数字和常见标点
    val cleaned = withoutBraces.replace(Regex("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s,，、·]"), "")

    // 清理多余的空格和逗号
    return cleaned.replace(Regex("\\s+"), " ")
                  .replace(Regex("[,，]+"), "，")
                  .trim()
}

// 🎬 工具函数：获取站点显示名称
private fun getSiteDisplayName(siteKey: String): String {
    return try {
        val site = top.cywin.onetv.movie.api.config.VodConfig.get().getSite(siteKey)
        site?.name ?: siteKey.replace("onetv_", "").replace("_", " ")
    } catch (e: Exception) {
        Log.w("MovieDetailScreen", "获取站点名称失败: $siteKey", e)
        siteKey.replace("onetv_", "").replace("_", " ")
    }
}

// 🔥 新增：内嵌视频播放器组件 - 使用与原版FongMi_TV相同的Players架构
@Composable
private fun EmbeddedVideoPlayer(
    playUrl: String,
    isPlaying: Boolean,
    onPlayPause: () -> Unit,
    onFullScreen: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var showControls by remember { mutableStateOf(false) }

    // 🔥 创建PlayerView - 与原版完全一致的配置
    val playerView = remember {
        androidx.media3.ui.PlayerView(context).apply {
            useController = false // 使用自定义控制器，与原版一致
            setShowBuffering(androidx.media3.ui.PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
            // 🔥 关键修复：设置播放器视图的点击监听 - 只进入全屏，不触发解析
            setOnClickListener {
                // 🔥 关键修复：点击播放器只进入全屏模式，不触发播放地址解析
                Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [全屏点击] 用户点击播放器，直接进入全屏模式，不触发解析")
                try {
                    onFullScreen()
                } catch (e: Exception) {
                    Log.e("ONETV_EMBEDDED_PLAYER", "🎬 [全屏点击] 进入全屏模式失败", e)
                }
            }
        }
    }

    // 🔥 使用原版FongMi_TV的Players播放器 - 完整功能版本
    val players = remember(playerView) {
        val activity = context as? android.app.Activity
        if (activity != null) {
            Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [播放器创建] 使用原版FongMi_TV Players架构")
            val playersInstance = top.cywin.onetv.movie.player.Players.create(activity)
            // 🔥 关键修复：必须调用init()方法来初始化ExoPlayer
            playersInstance.init(playerView)
            Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [播放器初始化] Players.init()调用完成，ExoPlayer已创建")
            playersInstance
        } else {
            null
        }
    }

    // 🔥 播放器初始化和播放URL设置 - 使用原版Players的完整功能
    LaunchedEffect(playUrl) {
        if (playUrl.isNotEmpty() && players != null) {
            Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [播放器初始化] 使用原版Players设置播放地址: $playUrl")
            try {
                // 🔥 关键：使用原版Players的start方法，支持完整的播放功能
                val result = top.cywin.onetv.movie.bean.Result().apply {
                    setPlayUrl(playUrl)
                    setParse(0) // 不需要解析
                }
                players.start(result, false, 30000L) // 30秒超时
                Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [播放器初始化] 播放器启动成功")
            } catch (e: Exception) {
                Log.e("ONETV_EMBEDDED_PLAYER", "🎬 [播放器初始化] 播放器启动失败", e)
            }
        }
    }

    // 🔥 播放状态控制 - 使用原版Players的播放控制方法
    LaunchedEffect(isPlaying) {
        if (players != null) {
            try {
                if (isPlaying) {
                    Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [播放控制] 使用原版Players开始播放")
                    players.play()
                } else {
                    Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [播放控制] 使用原版Players暂停播放")
                    players.pause()
                }
            } catch (e: Exception) {
                Log.e("ONETV_EMBEDDED_PLAYER", "🎬 [播放控制] 播放控制失败", e)
            }
        }
    }

    // 🔥 组件销毁时释放资源 - 使用原版Players的资源管理
    DisposableEffect(Unit) {
        onDispose {
            Log.d("ONETV_EMBEDDED_PLAYER", "🎬 [播放器销毁] 使用原版Players释放播放器资源")
            try {
                players?.stop()
                players?.release()
            } catch (e: Exception) {
                Log.e("ONETV_EMBEDDED_PLAYER", "🎬 [播放器销毁] 资源释放失败", e)
            }
        }
    }

    // 🔥 控制器自动隐藏
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(3000) // 3秒后自动隐藏
            showControls = false
        }
    }

    Box(
        modifier = modifier.background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        if (playUrl.isNotEmpty()) {
            // 🔥 使用AndroidView显示PlayerView - 与原版FongMi_TV完全一致
            AndroidView(
                factory = { playerView },
                modifier = Modifier.fillMaxSize()
            )

            // 🔥 内嵌播放器控制器 - 简化版但功能完整
            AnimatedVisibility(
                visible = showControls,
                enter = fadeIn(),
                exit = fadeOut(),
                modifier = Modifier.align(Alignment.Center)
            ) {
                EmbeddedPlayerControls(
                    isPlaying = isPlaying,
                    onPlayPause = onPlayPause,
                    onFullScreen = onFullScreen
                )
            }
        } else {
            // 🔥 加载状态显示
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(32.dp)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "正在解析播放地址...",
                    color = Color.White,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

// 🔥 内嵌播放器控制器组件
@Composable
private fun EmbeddedPlayerControls(
    isPlaying: Boolean,
    onPlayPause: () -> Unit,
    onFullScreen: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.6f),
                RoundedCornerShape(24.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 播放/暂停按钮
        IconButton(
            onClick = onPlayPause,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                contentDescription = if (isPlaying) "暂停" else "播放",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }

        // 全屏按钮
        IconButton(
            onClick = onFullScreen,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Fullscreen,
                contentDescription = "全屏播放",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}
