package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.ui.components.MovieCard
import top.cywin.onetv.movie.viewmodel.MovieSearchResultListViewModel

/**
 * 搜索结果列表页面 - 左侧站点列表，右侧电影卡片列表
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MovieSearchResultListScreen(
    keyword: String,
    navController: NavController,
    viewModel: MovieSearchResultListViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    // 🔥 修复逻辑：页面初始化时设置空结果状态，不显示加载界面
    LaunchedEffect(keyword) {
        android.util.Log.d("ONETV_SEARCH_RESULT_LIST", "🔍 [修复逻辑] 搜索结果列表页面初始化: $keyword")
        android.util.Log.d("ONETV_SEARCH_RESULT_LIST", "🚀 [修复逻辑] 设置初始空结果状态，准备动态接收搜索结果")

        // 🔥 关键修复：设置初始空结果状态，不显示加载界面，实现瞬间跳转
        viewModel.setInitialEmptyState(keyword)
    }

    // 监听导航事件，接收搜索结果数据
    DisposableEffect(keyword) {
        android.util.Log.d("ONETV_SEARCH_RESULT_LIST", "🔍 [修改逻辑] 注册搜索结果监听器: $keyword")

        // 创建EventBus监听器来接收动态搜索结果
        val eventListener = object {
            @org.greenrobot.eventbus.Subscribe(threadMode = org.greenrobot.eventbus.ThreadMode.MAIN)
            fun onSearchResultUpdate(event: top.cywin.onetv.movie.event.SearchResultEvent) {
                if (event.keyword == keyword) {
                    android.util.Log.d("ONETV_SEARCH_RESULT_LIST", "🔄 [修复逻辑] 接收到动态搜索结果: ${event.keyword}, 新增结果数: ${event.results.size}")
                    viewModel.addSearchResults(event)
                }
            }
        }

        // 注册EventBus监听
        if (!org.greenrobot.eventbus.EventBus.getDefault().isRegistered(eventListener)) {
            org.greenrobot.eventbus.EventBus.getDefault().register(eventListener)
        }

        // 清理EventBus监听
        onDispose {
            try {
                if (org.greenrobot.eventbus.EventBus.getDefault().isRegistered(eventListener)) {
                    org.greenrobot.eventbus.EventBus.getDefault().unregister(eventListener)
                }
            } catch (e: Exception) {
                android.util.Log.w("ONETV_SEARCH_RESULT_LIST", "EventBus取消注册失败", e)
            }
        }
    }

    // 🔥 删除这个LaunchedEffect，它会导致显示"正在搜索..."界面
    // 现在使用setInitialEmptyState()来实现瞬间跳转

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部工具栏
        TopAppBar(
            title = {
                Column {
                    Text(
                        text = "搜索: $keyword",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    // 显示站点数量统计
                    if (uiState.sites.isNotEmpty()) {
                        Text(
                            text = "站点数: ${uiState.sites.size} | 结果: ${uiState.allResults.size}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            },
            navigationIcon = {
                IconButton(onClick = {
                    android.util.Log.d("ONETV_SEARCH_RESULT_LIST", "🔙 [修复逻辑] 用户点击返回按钮 - 强制返回主界面")

                    // 🔧 修复：清理ViewModel状态，避免卡在加载状态
                    viewModel.clearSearchState()

                    // 🔧 修复：强制清理所有搜索相关状态
                    try {
                        // 停止所有正在进行的搜索
                        org.greenrobot.eventbus.EventBus.getDefault().post(
                            top.cywin.onetv.movie.event.SearchErrorEvent(keyword, "用户取消搜索")
                        )
                    } catch (e: Exception) {
                        android.util.Log.w("ONETV_SEARCH_RESULT_LIST", "清理搜索状态时出错", e)
                    }

                    // 🔥 强制返回：清除所有页面栈，直接跳转到主界面
                    try {
                        navController.navigate("movie_home") {
                            popUpTo(0) { inclusive = true }  // 清除所有页面
                            launchSingleTop = true
                        }
                        android.util.Log.d("ONETV_SEARCH_RESULT_LIST", "✅ [修复逻辑] 已强制返回主界面")
                    } catch (e: Exception) {
                        android.util.Log.e("ONETV_SEARCH_RESULT_LIST", "❌ [修复逻辑] 返回主界面失败", e)
                        // 备用方案：直接popBackStack
                        navController.popBackStack("movie_home", false)
                    }
                }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            }
        )

        // 内容区域
        when {
            uiState.isLoading -> {
                LoadingScreen(message = "正在搜索...")
            }
            uiState.error != null -> {
                ErrorScreen(
                    error = uiState.error ?: "未知错误",
                    onRetry = { viewModel.searchMovies(keyword) },
                    onBack = {
                        // 🔧 修复：错误界面的返回也要直接回到主界面
                        android.util.Log.d("ONETV_SEARCH_RESULT_LIST", "🔙 [修复逻辑] 错误界面返回 - 直接返回主界面")
                        try {
                            navController.navigate("movie_home") {
                                popUpTo(0) { inclusive = true }
                                launchSingleTop = true
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("ONETV_SEARCH_RESULT_LIST", "❌ [修复逻辑] 错误界面返回失败", e)
                            navController.popBackStack("movie_home", false)
                        }
                    }
                )
            }
            uiState.allResults.isEmpty() -> {
                // 🔥 修复逻辑：显示空的搜索结果列表，实现瞬间跳转
                SearchResultListContent(
                    uiState = uiState.copy(
                        allResults = emptyList(),
                        filteredResults = emptyList(),
                        sites = emptyList()
                    ),
                    onSiteSelect = { site -> viewModel.selectSite(site) },
                    onMovieClick = { movie ->
                        // 🔥 生成FlowID用于全流程跟踪
                        val flowId = "MOVIE_${System.currentTimeMillis()}"
                        val startTime = System.currentTimeMillis()

                        android.util.Log.d("VOD_FLOW", "=== [FlowID:$flowId] === 用户点击电影开始 ===")
                        android.util.Log.d("VOD_FLOW", "[FlowID:$flowId] [MOVIE_CLICK_START] 用户点击电影: ${movie.vodName}")
                        android.util.Log.d("VOD_FLOW", "[FlowID:$flowId] [MOVIE_CLICK_INFO] 电影信息 - vodId: ${movie.vodId}, siteKey: ${movie.siteKey}")
                        android.util.Log.d("-VOD_FLOW", "│ [${java.text.SimpleDateFormat("HH:mm:ss.SSS").format(java.util.Date())}] [FlowID:$flowId] [MOVIE_CLICK_START] 用户点击电影: ${movie.vodName}")

                        // 🔥 关键修复：设置全局FlowID，确保跨页面传递
                        top.cywin.onetv.movie.utils.VodFlowTracker.setGlobalFlowId(flowId)

                        // 🔥 修复：直接导航到详情页面，不通过RepositoryAdapter
                        // 正确的用户流程：点击电影卡片 → 立即进入详情页面 → 详情页面内部处理数据加载
                        try {
                            val route = top.cywin.onetv.movie.navigation.MovieRoutes.detail(movie.vodId, movie.siteKey)
                            android.util.Log.d("VOD_FLOW", "[FlowID:$flowId] [NAVIGATION_START] 开始导航到详情页面: $route")
                            android.util.Log.d("-VOD_FLOW", "│ [${java.text.SimpleDateFormat("HH:mm:ss.SSS").format(java.util.Date())}] [FlowID:$flowId] [NAVIGATION_START] 开始导航到详情页面")

                            navController.navigate(route)

                            val endTime = System.currentTimeMillis()
                            android.util.Log.d("VOD_FLOW", "[FlowID:$flowId] [NAVIGATION_SUCCESS] 导航到详情页面成功，耗时: ${endTime - startTime}ms")
                            android.util.Log.d("-VOD_FLOW", "│ [${java.text.SimpleDateFormat("HH:mm:ss.SSS").format(java.util.Date())}] [FlowID:$flowId] [NAVIGATION_SUCCESS] 导航成功，耗时: ${endTime - startTime}ms")
                        } catch (e: Exception) {
                            android.util.Log.e("VOD_FLOW", "[FlowID:$flowId] [NAVIGATION_ERROR] 导航失败: ${e.message}", e)
                            android.util.Log.e("-VOD_FLOW", "│ [${java.text.SimpleDateFormat("HH:mm:ss.SSS").format(java.util.Date())}] [FlowID:$flowId] [NAVIGATION_ERROR] 导航失败")
                        }
                    }
                )
            }
            else -> {
                SearchResultListContent(
                    uiState = uiState,
                    onSiteSelect = { site -> viewModel.selectSite(site) },
                    onMovieClick = { movie ->
                        // 🔥 生成FlowID用于全流程跟踪
                        val flowId = "MOVIE_${System.currentTimeMillis()}"
                        val startTime = System.currentTimeMillis()

                        android.util.Log.d("VOD_FLOW", "=== [FlowID:$flowId] === 用户点击电影开始 ===")
                        android.util.Log.d("VOD_FLOW", "[FlowID:$flowId] [MOVIE_CLICK_START] 用户点击电影: ${movie.vodName}")
                        android.util.Log.d("VOD_FLOW", "[FlowID:$flowId] [MOVIE_CLICK_INFO] 电影信息 - vodId: ${movie.vodId}, siteKey: ${movie.siteKey}")
                        android.util.Log.d("-VOD_FLOW", "│ [${java.text.SimpleDateFormat("HH:mm:ss.SSS").format(java.util.Date())}] [FlowID:$flowId] [MOVIE_CLICK_START] 用户点击电影: ${movie.vodName}")

                        // 🔥 修复：直接导航到详情页面，不通过RepositoryAdapter
                        // 正确的用户流程：点击电影卡片 → 立即进入详情页面 → 详情页面内部处理数据加载
                        try {
                            val route = top.cywin.onetv.movie.navigation.MovieRoutes.detail(movie.vodId, movie.siteKey)
                            android.util.Log.d("VOD_FLOW", "[FlowID:$flowId] [NAVIGATION_START] 开始导航到详情页面: $route")
                            android.util.Log.d("-VOD_FLOW", "│ [${java.text.SimpleDateFormat("HH:mm:ss.SSS").format(java.util.Date())}] [FlowID:$flowId] [NAVIGATION_START] 开始导航到详情页面")

                            navController.navigate(route)

                            val endTime = System.currentTimeMillis()
                            android.util.Log.d("VOD_FLOW", "[FlowID:$flowId] [NAVIGATION_SUCCESS] 导航到详情页面成功，耗时: ${endTime - startTime}ms")
                            android.util.Log.d("-VOD_FLOW", "│ [${java.text.SimpleDateFormat("HH:mm:ss.SSS").format(java.util.Date())}] [FlowID:$flowId] [NAVIGATION_SUCCESS] 导航成功，耗时: ${endTime - startTime}ms")
                        } catch (e: Exception) {
                            android.util.Log.e("VOD_FLOW", "[FlowID:$flowId] [NAVIGATION_ERROR] 导航失败: ${e.message}", e)
                            android.util.Log.e("-VOD_FLOW", "│ [${java.text.SimpleDateFormat("HH:mm:ss.SSS").format(java.util.Date())}] [FlowID:$flowId] [NAVIGATION_ERROR] 导航失败")
                        }
                    }
                )
            }
        }
    }
}

@Composable
private fun SearchResultListContent(
    uiState: MovieSearchResultListUiState,
    onSiteSelect: (SiteInfo?) -> Unit,
    onMovieClick: (MovieItem) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 左侧站点列表
        SiteFilterPanel(
            modifier = Modifier
                .width(200.dp)
                .fillMaxHeight(),
            sites = uiState.sites,
            selectedSite = uiState.selectedSite,
            onSiteSelect = onSiteSelect
        )

        Spacer(modifier = Modifier.width(12.dp))

        // 🎨 优化分隔线 - 符合项目整体风格的竖直分隔线
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .width(2.dp)
                .background(
                    brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.6f),
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.6f),
                            Color.Transparent
                        )
                    )
                )
        )

        Spacer(modifier = Modifier.width(12.dp))

        // 右侧电影网格
        SearchResultMovieGrid(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight(),
            movies = uiState.filteredResults,
            onMovieClick = onMovieClick
        )
    }
}

@Composable
private fun SiteFilterPanel(
    modifier: Modifier = Modifier,
    sites: List<SiteInfo>,
    selectedSite: SiteInfo?,
    onSiteSelect: (SiteInfo?) -> Unit
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.3f)
        )
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp, vertical = 8.dp), // 🔧 减少顶部padding，让内容更靠上
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 全部显示选项 - 🔧 调整顶部对齐，与右侧电影卡片顶端齐平
            item {
                SiteFilterItem(
                    siteName = "全部显示",
                    resultCount = sites.sumOf { it.resultCount },
                    isSelected = selectedSite == null,
                    onClick = { onSiteSelect(null) }
                )
            }

            // 各站点选项
            items(sites) { site ->
                SiteFilterItem(
                    siteName = site.siteName,
                    resultCount = site.resultCount,
                    isSelected = selectedSite?.siteKey == site.siteKey,
                    onClick = { onSiteSelect(site) }
                )
            }
        }
    }
}

@Composable
private fun SiteFilterItem(
    siteName: String,
    resultCount: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                // 选中状态：蓝色渐变背景
                Color(0xFF1976D2)
            } else {
                // 未选中状态：深色半透明背景
                Color(0xFF2A2A2A).copy(alpha = 0.8f)
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 6.dp else 2.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = siteName,
                color = if (isSelected) Color.White else Color(0xFFE0E0E0),
                fontSize = 14.sp,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 结果数量标签
            Surface(
                shape = RoundedCornerShape(16.dp),
                color = if (isSelected) {
                    Color.White.copy(alpha = 0.2f)
                } else {
                    Color(0xFF4CAF50).copy(alpha = 0.8f)
                }
            ) {
                Text(
                    text = "$resultCount",
                    color = if (isSelected) Color.White else Color.White,
                    fontSize = 11.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
        }
    }
}

@Composable
private fun SearchResultMovieGrid(
    modifier: Modifier = Modifier,
    movies: List<MovieItem>,
    onMovieClick: (MovieItem) -> Unit
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(5),
        modifier = modifier,
        contentPadding = PaddingValues(top = 8.dp, bottom = 16.dp), // 🔧 调整顶部padding，与左侧站点列表对齐
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(movies) { movie ->
            MovieCard(
                movie = movie,
                onClick = { onMovieClick(movie) },
                showSiteInfo = true // 在搜索结果页面显示站点信息
            )
        }
    }
}

@Composable
private fun NoResultContent(keyword: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "未找到相关内容",
                style = MaterialTheme.typography.titleLarge,
                color = Color.White
            )
            Text(
                text = "搜索关键词: $keyword",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.7f)
            )
        }
    }
}

// 数据类定义
data class MovieSearchResultListUiState(
    val keyword: String = "",
    val isLoading: Boolean = false,
    val error: String? = null,
    val sites: List<SiteInfo> = emptyList(),
    val selectedSite: SiteInfo? = null,
    val allResults: List<MovieItem> = emptyList(),
    val filteredResults: List<MovieItem> = emptyList(),
    val hasMore: Boolean = false
)

data class SiteInfo(
    val siteKey: String,
    val siteName: String,
    val resultCount: Int,
    val isSelected: Boolean = false
)

// ===== 私有辅助Composable函数 =====

@Composable
private fun LoadingScreen(message: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = message)
        }
    }
}

@Composable
private fun SearchingContent(keyword: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CircularProgressIndicator()
            Text(
                text = "正在搜索「$keyword」...",
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = "搜索结果将动态更新",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(onClick = onRetry) {
                    Text("重试")
                }
                OutlinedButton(onClick = onBack) {
                    Text("返回")
                }
            }
        }
    }
}
